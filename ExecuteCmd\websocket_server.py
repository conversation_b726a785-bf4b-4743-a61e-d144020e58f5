#!/usr/bin/env python3
"""
WebSocket server for real-time execution updates.
Handles real-time communication between ExecuteCmd and frontend.
"""

import asyncio
import websockets
import json
import logging
import threading
from typing import Set, Dict, Any
# Remove unused import

logger = logging.getLogger("WebSocketServer")

class ExecutionWebSocketServer:
    """
    WebSocket server for real-time execution updates.
    """
    
    def __init__(self, host: str = "localhost", port: int = 8006):
        self.host = host
        self.port = port
        self.connected_clients: Set = set()
        self.execution_subscribers: Dict[str, Set] = {}
        self.server = None
        self.running = False

    async def register_client(self, websocket, execution_id: str = None):
        """Register a new WebSocket client."""
        self.connected_clients.add(websocket)
        
        if execution_id:
            if execution_id not in self.execution_subscribers:
                self.execution_subscribers[execution_id] = set()
            self.execution_subscribers[execution_id].add(websocket)
        
        logger.info(f"📡 Client connected. Total: {len(self.connected_clients)}")
        
        # Send welcome message
        await self.send_to_client(websocket, {
            "type": "connection",
            "status": "connected",
            "message": "WebSocket connection established"
        })
    
    async def unregister_client(self, websocket):
        """Unregister a WebSocket client."""
        self.connected_clients.discard(websocket)

        # Remove from execution subscribers
        for execution_id, subscribers in self.execution_subscribers.items():
            subscribers.discard(websocket)

        logger.info(f"📡 Client disconnected. Total: {len(self.connected_clients)}")
    
    async def handle_client(self, websocket, path):
        """Handle WebSocket client connection."""
        try:
            # Extract execution_id from path if present
            execution_id = None
            if path and path.startswith('/execution/'):
                execution_id = path.split('/')[-1]

            await self.register_client(websocket, execution_id)
            
            # Handle incoming messages
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.handle_message(websocket, data)
                except json.JSONDecodeError:
                    await self.send_to_client(websocket, {
                        "type": "error",
                        "message": "Invalid JSON message"
                    })
                except Exception as e:
                    logger.error(f"❌ Error handling message: {e}")
                    await self.send_to_client(websocket, {
                        "type": "error",
                        "message": f"Message handling error: {str(e)}"
                    })
        
        except websockets.exceptions.ConnectionClosed:
            logger.info("📡 Client connection closed")
        except Exception as e:
            logger.error(f"❌ WebSocket error: {e}")
        finally:
            await self.unregister_client(websocket)
    
    async def handle_message(self, websocket, data: Dict[str, Any]):
        """Handle incoming WebSocket message."""
        message_type = data.get('type')

        if message_type == 'subscribe':
            execution_id = data.get('execution_id')
            if execution_id:
                if execution_id not in self.execution_subscribers:
                    self.execution_subscribers[execution_id] = set()
                self.execution_subscribers[execution_id].add(websocket)

                await self.send_to_client(websocket, {
                    "type": "subscribed",
                    "execution_id": execution_id,
                    "message": f"Subscribed to execution {execution_id}"
                })

        elif message_type == 'ping':
            await self.send_to_client(websocket, {
                "type": "pong",
                "timestamp": data.get('timestamp')
            })

        else:
            await self.send_to_client(websocket, {
                "type": "error",
                "message": f"Unknown message type: {message_type}"
            })

    async def send_to_client(self, websocket, data: Dict[str, Any]):
        """Send data to a specific client."""
        try:
            message = json.dumps(data)
            await websocket.send(message)
        except websockets.exceptions.ConnectionClosed:
            await self.unregister_client(websocket)
        except Exception as e:
            logger.error(f"❌ Error sending to client: {e}")
    
    async def broadcast_to_execution(self, execution_id: str, data: Dict[str, Any]):
        """Broadcast data to all clients subscribed to a specific execution."""
        if execution_id not in self.execution_subscribers:
            return
        
        # Add execution context to data
        data['execution_id'] = execution_id
        data['type'] = 'execution_update'
        
        subscribers = self.execution_subscribers[execution_id].copy()
        
        for websocket in subscribers:
            await self.send_to_client(websocket, data)
    
    async def broadcast_to_all(self, data: Dict[str, Any]):
        """Broadcast data to all connected clients."""
        if not self.connected_clients:
            return
        
        clients = self.connected_clients.copy()
        
        for websocket in clients:
            await self.send_to_client(websocket, data)
    
    async def start_server(self):
        """Start the WebSocket server."""
        logger.info(f"🚀 Starting WebSocket server on {self.host}:{self.port}")
        
        self.server = await websockets.serve(
            self.handle_client,
            self.host,
            self.port,
            ping_interval=30,
            ping_timeout=10
        )
        
        self.running = True
        logger.info(f"✅ WebSocket server running on ws://{self.host}:{self.port}")
    
    async def stop_server(self):
        """Stop the WebSocket server."""
        if self.server:
            logger.info("🛑 Stopping WebSocket server...")
            self.server.close()
            await self.server.wait_closed()
            self.running = False
            logger.info("✅ WebSocket server stopped")
    
    def start_in_thread(self):
        """Start the WebSocket server in a separate thread."""
        def run_server():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.start_server())
            loop.run_forever()
        
        thread = threading.Thread(target=run_server, daemon=True)
        thread.start()
        logger.info("🧵 WebSocket server started in background thread")
        return thread

# Global WebSocket server instance
websocket_server = ExecutionWebSocketServer()

async def broadcast_execution_update(execution_id: str, update_data: Dict[str, Any]):
    """Helper function to broadcast execution updates."""
    await websocket_server.broadcast_to_execution(execution_id, update_data)

def start_websocket_server():
    """Start the WebSocket server in background."""
    return websocket_server.start_in_thread()

def get_websocket_server():
    """Get the global WebSocket server instance."""
    return websocket_server
