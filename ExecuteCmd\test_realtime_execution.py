#!/usr/bin/env python3
"""
Test client for real-time execution with WebSocket updates.
Demonstrates the new step-by-step execution with immediate updates.
"""

import asyncio
import websockets
import requests
import json
import time
import sys
from typing import Dict, Any

class RealtimeExecutionClient:
    """
    Client for testing real-time execution with WebSocket updates.
    """
    
    def __init__(self, base_url: str = "http://localhost:8007", websocket_url: str = "ws://localhost:8006"):
        self.base_url = base_url
        self.websocket_url = websocket_url
        self.execution_id = None
        self.websocket = None
        self.step_updates = []
    
    async def start_execution(self, repository_url: str, setup_chain: list):
        """Start a real-time execution."""
        print(f"🚀 Starting real-time execution for: {repository_url}")
        
        payload = {
            "repository_url": repository_url,
            "setup_chain": setup_chain
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/execute-setup-chain-realtime",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                self.execution_id = result["execution_id"]
                print(f"✅ Execution started: {self.execution_id}")
                print(f"📡 WebSocket URL: {result['websocket_url']}")
                return True
            else:
                print(f"❌ Failed to start execution: {response.status_code}")
                print(f"Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error starting execution: {e}")
            return False
    
    async def connect_websocket(self):
        """Connect to WebSocket for real-time updates."""
        if not self.execution_id:
            print("❌ No execution ID available")
            return False
        
        try:
            websocket_url = f"{self.websocket_url}/execution/{self.execution_id}"
            print(f"📡 Connecting to WebSocket: {websocket_url}")
            
            self.websocket = await websockets.connect(websocket_url)
            print("✅ WebSocket connected")
            
            # Subscribe to execution updates
            await self.websocket.send(json.dumps({
                "type": "subscribe",
                "execution_id": self.execution_id
            }))
            
            return True
            
        except Exception as e:
            print(f"❌ WebSocket connection error: {e}")
            return False
    
    async def listen_for_updates(self):
        """Listen for real-time updates via WebSocket."""
        if not self.websocket:
            print("❌ WebSocket not connected")
            return
        
        print("👂 Listening for real-time updates...")
        print("=" * 60)
        
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self.handle_update(data)
                except json.JSONDecodeError:
                    print(f"❌ Invalid JSON received: {message}")
                except Exception as e:
                    print(f"❌ Error handling update: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            print("📡 WebSocket connection closed")
        except Exception as e:
            print(f"❌ WebSocket error: {e}")
    
    async def handle_update(self, data: Dict[str, Any]):
        """Handle incoming WebSocket update."""
        update_type = data.get('type')
        
        if update_type == 'connection':
            print(f"📡 {data.get('message', 'Connected')}")
        
        elif update_type == 'subscribed':
            print(f"📡 Subscribed to execution {data.get('execution_id')}")
        
        elif update_type == 'execution_update':
            await self.handle_execution_update(data)
        
        elif update_type == 'error':
            print(f"❌ WebSocket error: {data.get('message')}")
        
        else:
            print(f"📡 Unknown update type: {update_type}")
    
    async def handle_execution_update(self, data: Dict[str, Any]):
        """Handle execution-specific updates."""
        current_step = data.get('current_step', 0)
        total_steps = data.get('total_steps', 0)
        status = data.get('status', 'unknown')
        message = data.get('message', '')
        progress = data.get('progress_percentage', 0)
        
        timestamp = time.strftime("%H:%M:%S")
        
        # General execution update
        print(f"[{timestamp}] 📊 Step {current_step}/{total_steps} ({progress:.1f}%) - {status}")
        if message:
            print(f"           💬 {message}")
        
        # Step-specific update
        step_result = data.get('step_result')
        if step_result:
            await self.handle_step_update(step_result)
        
        # Check if execution completed
        if status in ['success', 'failed']:
            print(f"\n🏁 Execution completed with status: {status}")
            await self.show_execution_summary()
    
    async def handle_step_update(self, step_result: Dict[str, Any]):
        """Handle step-specific updates."""
        step_number = step_result.get('step_number', 0)
        title = step_result.get('title', 'Unknown Step')
        status = step_result.get('status', 'unknown')
        command = step_result.get('command', '')
        duration = step_result.get('duration', 0)
        fix_applied = step_result.get('fix_applied', '')
        
        # Status icons
        status_icons = {
            'pending': '⏳',
            'running': '🔄',
            'success': '✅',
            'failed': '❌',
            'analyzing': '🤖',
            'fixing': '🔧'
        }
        
        icon = status_icons.get(status, '❓')
        
        print(f"           {icon} Step {step_number}: {title} ({status})")
        
        if command:
            print(f"              💻 Command: {command}")
        
        if fix_applied:
            print(f"              🔧 AI Fix: {fix_applied}")
        
        if duration > 0:
            print(f"              ⏱️ Duration: {duration:.1f}s")
        
        # Store step update
        self.step_updates.append(step_result)
    
    async def show_execution_summary(self):
        """Show execution summary."""
        if not self.execution_id:
            return
        
        try:
            response = requests.get(f"{self.base_url}/execution/{self.execution_id}/summary")
            
            if response.status_code == 200:
                summary = response.json()
                
                print(f"\n📋 Execution Summary:")
                print(f"   Execution ID: {summary['execution_id']}")
                print(f"   Repository: {summary['repository_url']}")
                print(f"   Total Steps: {summary['total_steps']}")
                print(f"   Completed: {summary['completed_steps']}")
                print(f"   Failed: {summary['failed_steps']}")
                print(f"   Docker Used: {summary['use_docker']}")
                
                # Show step results
                step_results = summary.get('step_results', [])
                if step_results:
                    print(f"\n📝 Step Results:")
                    for step in step_results:
                        status_icon = '✅' if step['status'] == 'success' else '❌' if step['status'] == 'failed' else '⏳'
                        print(f"   {status_icon} Step {step['step_number']}: {step['title']} ({step['duration']:.1f}s)")
                        if step.get('fix_applied'):
                            print(f"      🔧 Fix: {step['fix_applied']}")
            
        except Exception as e:
            print(f"❌ Error getting summary: {e}")
    
    async def close(self):
        """Close WebSocket connection."""
        if self.websocket:
            await self.websocket.close()
            print("📡 WebSocket connection closed")

async def test_realtime_execution():
    """Test the real-time execution system."""
    print("🧪 Real-time Execution Test")
    print("Testing step-by-step execution with WebSocket updates")
    print("=" * 60)
    
    # Create client
    client = RealtimeExecutionClient()
    
    # Test setup chain (simplified for testing)
    setup_chain = [
        {
            "commands": [
                "python3 --version",
                "git --version"
            ],
            "description": "Check basic tools availability",
            "estimated_time": "30 seconds",
            "notes": ["Basic version checks"],
            "prerequisites": [],
            "step_number": 1,
            "title": "Prerequisites Check",
            "working_directory": "~/"
        },
        {
            "commands": [
                "git clone https://github.com/AliSoua/HR-Management.git",
                "ls -la HR-Management"
            ],
            "description": "Clone repository and verify",
            "estimated_time": "1-2 minutes",
            "notes": [],
            "prerequisites": ["Git installed"],
            "step_number": 2,
            "title": "Clone Repository",
            "working_directory": "~/"
        },
        {
            "commands": [
                "python3 -m venv venv",
                "echo 'Virtual environment created'"
            ],
            "description": "Create Python virtual environment",
            "estimated_time": "1 minute",
            "notes": [],
            "prerequisites": ["Python installed"],
            "step_number": 3,
            "title": "Create Virtual Environment",
            "working_directory": "./HR-Management"
        }
    ]
    
    try:
        # Start execution
        success = await client.start_execution(
            "https://github.com/AliSoua/HR-Management",
            setup_chain
        )
        
        if not success:
            print("❌ Failed to start execution")
            return False
        
        # Connect to WebSocket
        connected = await client.connect_websocket()
        
        if not connected:
            print("❌ Failed to connect to WebSocket")
            return False
        
        # Listen for updates
        await client.listen_for_updates()
        
        return True
        
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False
    finally:
        await client.close()

def main():
    """Main test function."""
    print("🚀 Real-time ExecuteCmd Test Client")
    print("This demonstrates step-by-step execution with WebSocket updates")
    print("=" * 70)
    
    try:
        success = asyncio.run(test_realtime_execution())
        
        if success:
            print("\n🎉 Real-time execution test completed!")
        else:
            print("\n💥 Real-time execution test failed!")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted")
        return 1

if __name__ == "__main__":
    sys.exit(main())
