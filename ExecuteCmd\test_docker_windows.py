#!/usr/bin/env python3
"""
Docker connectivity test for Windows.
This script tests different Docker connection methods on Windows.
"""

import docker
import platform
import sys

def test_docker_connection():
    """Test Docker connectivity with different methods."""
    print("🐳 Testing Docker connectivity on Windows...")
    print(f"Platform: {platform.system()} {platform.release()}")
    
    connection_methods = []
    
    if platform.system() == 'Windows':
        connection_methods = [
            ("Named Pipe", lambda: docker.DockerClient(base_url='npipe:////./pipe/docker_engine')),
            ("TCP (2375)", lambda: docker.DockerClient(base_url='tcp://localhost:2375')),
            ("TCP with TLS (2376)", lambda: docker.DockerClient(base_url='tcp://localhost:2376', tls=True)),
            ("Default", lambda: docker.from_env())
        ]
    else:
        connection_methods = [
            ("Default", lambda: docker.from_env())
        ]
    
    successful_client = None
    
    for method_name, client_factory in connection_methods:
        try:
            print(f"\n🔍 Trying {method_name}...")
            client = client_factory()
            
            # Test connection
            version_info = client.version()
            client.ping()
            
            print(f"✅ {method_name} connection successful!")
            print(f"   Docker version: {version_info.get('Version', 'Unknown')}")
            print(f"   API version: {version_info.get('ApiVersion', 'Unknown')}")
            
            if not successful_client:
                successful_client = client
                
        except Exception as e:
            print(f"❌ {method_name} failed: {str(e)}")
    
    if successful_client:
        print(f"\n🎉 Docker is accessible! Testing container creation...")
        try:
            # Test creating a simple container
            container = successful_client.containers.run(
                'hello-world',
                detach=True,
                remove=True
            )
            print(f"✅ Container test successful!")
            
        except Exception as e:
            print(f"❌ Container test failed: {str(e)}")
            print("   This might be due to missing images or Docker permissions.")
    else:
        print(f"\n💥 No Docker connection method worked!")
        print("\n🔧 Troubleshooting tips:")
        print("1. Make sure Docker Desktop is running")
        print("2. Check if Docker is in Windows containers mode (not Linux containers)")
        print("3. Try enabling 'Expose daemon on tcp://localhost:2375 without TLS' in Docker Desktop settings")
        print("4. Restart Docker Desktop")
        print("5. Run this script as Administrator")
        
        return False
    
    return True

def check_docker_desktop():
    """Check if Docker Desktop is running."""
    try:
        import subprocess
        result = subprocess.run(['docker', 'version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ Docker CLI is working")
            return True
        else:
            print(f"❌ Docker CLI failed: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ Docker CLI timeout - Docker might not be running")
        return False
    except FileNotFoundError:
        print("❌ Docker CLI not found - Docker might not be installed")
        return False
    except Exception as e:
        print(f"❌ Docker CLI error: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 ExecuteCmd Docker Connectivity Test")
    print("=" * 50)
    
    # Check Docker CLI first
    print("\n📋 Checking Docker CLI...")
    cli_works = check_docker_desktop()
    
    if not cli_works:
        print("\n💡 Docker CLI is not working. Please:")
        print("   1. Install Docker Desktop")
        print("   2. Start Docker Desktop")
        print("   3. Wait for Docker to fully start")
        return 1
    
    # Test Python Docker library
    print("\n📋 Testing Python Docker library...")
    if test_docker_connection():
        print("\n🎉 All Docker tests passed!")
        print("   ExecuteCmd should work properly now.")
        return 0
    else:
        print("\n💥 Docker connectivity tests failed!")
        print("   ExecuteCmd will not work until Docker connectivity is fixed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
