import os
import logging
import json
import re
from typing import List, Dict, Any, Optional
import google.generativeai as genai
from dataclasses import dataclass

logger = logging.getLogger("SetupChainAgent")

@dataclass
class SetupStep:
    """Represents a single step in the setup chain."""
    step_number: int
    title: str
    description: str
    commands: List[str]
    working_directory: str
    prerequisites: List[str]
    notes: List[str]
    estimated_time: str

class SetupChainAgent:
    """
    Agent that analyzes repository files and generates a comprehensive
    setup chain with commands to run the application.
    """
    
    def __init__(self, repository_url: str, files_analyzed: List[Dict], use_ai: bool = True):
        self.repository_url = repository_url
        self.files_analyzed = files_analyzed
        self.use_ai = use_ai
        self.gemini_model = None
        
        if use_ai:
            self._initialize_gemini()
    
    def _initialize_gemini(self):
        """Initialize the Gemini AI model."""
        try:
            api_key = os.getenv('GEMINI_API_KEY')
            if not api_key:
                logger.warning("GEMINI_API_KEY not found. AI features will be disabled.")
                return
            
            genai.configure(api_key=api_key)
            self.gemini_model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')
            logger.info("Gemini AI model initialized successfully.")
            
        except Exception as e:
            logger.error(f"Failed to initialize Gemini AI: {e}")
            self.gemini_model = None
    
    def generate_setup_chain(self) -> Dict[str, Any]:
        """
        Generate a comprehensive setup chain for the repository.
        """
        if self.use_ai and self.gemini_model:
            return self._generate_ai_setup_chain()
        else:
            return self._generate_template_setup_chain()
    
    def _generate_ai_setup_chain(self) -> Dict[str, Any]:
        """Generate setup chain using AI analysis."""
        try:
            # Prepare the context for AI
            context = self._prepare_ai_context()
            
            prompt = f"""
You are an expert DevOps engineer tasked with creating a comprehensive setup chain for a software repository.

REPOSITORY: {self.repository_url}

ANALYZED FILES AND THEIR CONTENT:
{context}

Your task is to generate a detailed, step-by-step setup chain that will allow someone to:
1. Clone and set up the repository
2. Install all dependencies
3. Configure the environment
4. Build the application (if needed)
5. Run the application successfully

Please provide your response as a JSON object with the following structure:
{{
  "setup_chain": [
    {{
      "step_number": 1,
      "title": "Step Title",
      "description": "Detailed description of what this step does",
      "commands": ["command1", "command2"],
      "working_directory": "./",
      "prerequisites": ["prerequisite1", "prerequisite2"],
      "notes": ["important note 1", "important note 2"],
      "estimated_time": "2-3 minutes"
    }}
  ],
  "summary": "Overall summary of the setup process",
  "total_estimated_time": "15-20 minutes",
  "architecture_overview": "Brief overview of the application architecture",
  "important_notes": ["critical note 1", "critical note 2"],
  "troubleshooting": [
    {{
      "issue": "Common issue description",
      "solution": "How to resolve it"
    }}
  ]
}}

IMPORTANT GUIDELINES:
1. Be specific with commands - include exact package manager commands, environment setup, etc.
2. Consider the technology stack detected from the files
3. Include proper error handling and verification steps
4. Mention any required external dependencies (Redis, databases, etc.)
5. Include environment variable setup with examples
6. Consider both development and production scenarios
7. Include port information and how to access the application
8. Be thorough but practical - someone should be able to follow this exactly
9. Include commands to verify each step worked correctly
10. Consider the order of operations carefully (dependencies before main app, etc.)
"""

            response = self.gemini_model.generate_content(prompt)
            
            # Parse the JSON response
            response_text = response.text.strip()
            
            # Extract JSON from the response (in case it's wrapped in markdown)
            json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
            if json_match:
                json_text = json_match.group(1)
            else:
                json_text = response_text
            
            try:
                result = json.loads(json_text)
                logger.info("Successfully generated AI-powered setup chain.")
                return result
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse AI response as JSON: {e}")
                logger.debug(f"AI Response: {response_text}")
                return self._generate_template_setup_chain()
                
        except Exception as e:
            logger.error(f"Error generating AI setup chain: {e}")
            return self._generate_template_setup_chain()
    
    def _prepare_ai_context(self) -> str:
        """Prepare context from analyzed files for AI."""
        context_parts = []
        
        for file_data in self.files_analyzed:
            path = file_data.get('path', 'Unknown')
            content = file_data.get('content', '')
            analysis = file_data.get('analysis', '')
            error = file_data.get('error')
            
            if error:
                context_parts.append(f"FILE: {path}\nERROR: {error}\n")
            else:
                # Truncate very long content
                if len(content) > 3000:
                    content = content[:3000] + "\n... (content truncated)"
                
                context_parts.append(f"FILE: {path}\n")
                if analysis:
                    context_parts.append(f"AI ANALYSIS: {analysis}\n")
                context_parts.append(f"CONTENT:\n{content}\n")
                context_parts.append("---\n")
        
        return "\n".join(context_parts)

    def _generate_template_setup_chain(self) -> Dict[str, Any]:
        """Generate a basic setup chain using template-based analysis."""
        logger.info("Generating template-based setup chain.")

        # Analyze the files to determine technology stack
        tech_stack = self._analyze_tech_stack()

        setup_steps = []
        step_number = 1

        # Step 1: Clone repository
        setup_steps.append({
            "step_number": step_number,
            "title": "Clone Repository",
            "description": "Clone the repository to your local machine",
            "commands": [
                f"git clone {self.repository_url}",
                f"cd {self._extract_repo_name()}"
            ],
            "working_directory": "./",
            "prerequisites": ["Git installed"],
            "notes": ["Make sure you have access to the repository"],
            "estimated_time": "1-2 minutes"
        })
        step_number += 1

        # Add technology-specific steps
        if tech_stack.get('has_python'):
            setup_steps.extend(self._get_python_setup_steps(step_number, tech_stack))
            step_number += len([s for s in setup_steps if s['step_number'] >= step_number])

        if tech_stack.get('has_nodejs'):
            setup_steps.extend(self._get_nodejs_setup_steps(step_number, tech_stack))
            step_number += len([s for s in setup_steps if s['step_number'] >= step_number])

        # Add environment setup if .env files detected
        if tech_stack.get('has_env_files'):
            setup_steps.append({
                "step_number": step_number,
                "title": "Environment Configuration",
                "description": "Set up environment variables",
                "commands": [
                    "cp .env.example .env",
                    "# Edit .env file with your actual values"
                ],
                "working_directory": f"./{self._extract_repo_name()}",
                "prerequisites": ["Repository cloned"],
                "notes": [
                    "Update .env file with your actual API keys and configuration",
                    "Check README.md for required environment variables"
                ],
                "estimated_time": "2-3 minutes"
            })
            step_number += 1

        # Add external dependencies setup
        if tech_stack.get('needs_redis'):
            setup_steps.append({
                "step_number": step_number,
                "title": "Start Redis Server",
                "description": "Start Redis server for task queuing",
                "commands": [
                    "# On macOS: brew services start redis",
                    "# On Ubuntu: sudo systemctl start redis-server",
                    "# On Windows: redis-server.exe",
                    "redis-cli ping  # Verify Redis is running"
                ],
                "working_directory": f"./{self._extract_repo_name()}",
                "prerequisites": ["Redis installed"],
                "notes": [
                    "Redis is required for Celery task queuing",
                    "Make sure Redis is accessible on default port 6379"
                ],
                "estimated_time": "1-2 minutes"
            })
            step_number += 1

        # Add run steps
        if tech_stack.get('has_python') and tech_stack.get('has_celery'):
            setup_steps.append({
                "step_number": step_number,
                "title": "Start Application Services",
                "description": "Start both Celery worker and Flask application",
                "commands": [
                    "# Terminal 1: Start Celery worker",
                    "python run_worker.py",
                    "",
                    "# Terminal 2: Start Flask API (in a new terminal)",
                    "waitress-serve --host 127.0.0.1 --port 5001 app:app"
                ],
                "working_directory": f"./{self._extract_repo_name()}/backend",
                "prerequisites": ["Dependencies installed", "Environment configured", "Redis running"],
                "notes": [
                    "You need to run these commands in separate terminals",
                    "The API will be available at http://localhost:5001",
                    "Make sure both processes are running for full functionality"
                ],
                "estimated_time": "2-3 minutes"
            })
            step_number += 1
        elif tech_stack.get('has_nodejs'):
            setup_steps.append({
                "step_number": step_number,
                "title": "Start Application",
                "description": "Start the Node.js application",
                "commands": [
                    "npm start"
                ],
                "working_directory": f"./{self._extract_repo_name()}",
                "prerequisites": ["Dependencies installed", "Environment configured"],
                "notes": [
                    "Check package.json for the correct start command",
                    "The application will be available on the configured port"
                ],
                "estimated_time": "1-2 minutes"
            })
            step_number += 1

        return {
            "setup_chain": setup_steps,
            "summary": f"Setup chain for {tech_stack.get('detected_type', 'application')} with {len(setup_steps)} steps",
            "total_estimated_time": f"{len(setup_steps) * 2}-{len(setup_steps) * 3} minutes",
            "architecture_overview": self._get_architecture_overview(tech_stack),
            "important_notes": [
                "Make sure all prerequisites are installed before starting",
                "Follow the steps in order for best results",
                "Check the README.md file for additional information"
            ],
            "troubleshooting": [
                {
                    "issue": "Dependencies installation fails",
                    "solution": "Make sure you have the correct version of package manager and internet connection"
                },
                {
                    "issue": "Environment variables not working",
                    "solution": "Double-check .env file format and restart the application"
                },
                {
                    "issue": "Application won't start",
                    "solution": "Check logs for specific error messages and ensure all external dependencies are running"
                }
            ]
        }

    def _analyze_tech_stack(self) -> Dict[str, Any]:
        """Analyze the files to determine the technology stack."""
        tech_stack = {
            'has_python': False,
            'has_nodejs': False,
            'has_env_files': False,
            'has_celery': False,
            'needs_redis': False,
            'detected_type': 'application'
        }

        for file_data in self.files_analyzed:
            path = file_data.get('path', '').lower()
            content = file_data.get('content', '').lower()

            # Python detection
            if path.endswith('.py') or 'requirements.txt' in path or 'setup.py' in path:
                tech_stack['has_python'] = True

            # Node.js detection
            if 'package.json' in path or path.endswith('.js') or path.endswith('.jsx'):
                tech_stack['has_nodejs'] = True

            # Environment files
            if '.env' in path or 'environment' in path:
                tech_stack['has_env_files'] = True

            # Celery detection
            if 'celery' in content or 'run_worker.py' in path:
                tech_stack['has_celery'] = True
                tech_stack['needs_redis'] = True

            # Redis detection
            if 'redis' in content:
                tech_stack['needs_redis'] = True

        # Determine application type
        if tech_stack['has_python'] and tech_stack['has_nodejs']:
            tech_stack['detected_type'] = 'full-stack application'
        elif tech_stack['has_python']:
            tech_stack['detected_type'] = 'Python application'
        elif tech_stack['has_nodejs']:
            tech_stack['detected_type'] = 'Node.js application'

        return tech_stack

    def _extract_repo_name(self) -> str:
        """Extract repository name from URL."""
        # Extract repo name from URL like https://github.com/user/repo
        parts = self.repository_url.rstrip('/').split('/')
        return parts[-1] if parts else 'repository'

    def _get_python_setup_steps(self, start_step: int, tech_stack: Dict) -> List[Dict]:
        """Get Python-specific setup steps."""
        steps = []

        # Virtual environment setup
        steps.append({
            "step_number": start_step,
            "title": "Create Python Virtual Environment",
            "description": "Create and activate a Python virtual environment",
            "commands": [
                "python -m venv venv",
                "# Activate virtual environment:",
                "# Windows: .\\venv\\Scripts\\activate",
                "# macOS/Linux: source venv/bin/activate"
            ],
            "working_directory": f"./{self._extract_repo_name()}",
            "prerequisites": ["Python 3.x installed", "Repository cloned"],
            "notes": [
                "Virtual environment isolates project dependencies",
                "Make sure to activate the environment before installing packages"
            ],
            "estimated_time": "1-2 minutes"
        })

        # Install Python dependencies
        steps.append({
            "step_number": start_step + 1,
            "title": "Install Python Dependencies",
            "description": "Install required Python packages",
            "commands": [
                "pip install -r requirements.txt"
            ],
            "working_directory": f"./{self._extract_repo_name()}",
            "prerequisites": ["Virtual environment activated"],
            "notes": [
                "This will install all required Python packages",
                "If there are multiple requirements.txt files, install them separately"
            ],
            "estimated_time": "2-5 minutes"
        })

        return steps

    def _get_nodejs_setup_steps(self, start_step: int, tech_stack: Dict) -> List[Dict]:
        """Get Node.js-specific setup steps."""
        steps = []

        # Install Node.js dependencies
        steps.append({
            "step_number": start_step,
            "title": "Install Node.js Dependencies",
            "description": "Install required Node.js packages",
            "commands": [
                "npm install"
            ],
            "working_directory": f"./{self._extract_repo_name()}",
            "prerequisites": ["Node.js and npm installed", "Repository cloned"],
            "notes": [
                "This will install all packages listed in package.json",
                "You can also use 'yarn install' if you prefer Yarn"
            ],
            "estimated_time": "2-5 minutes"
        })

        return steps

    def _get_architecture_overview(self, tech_stack: Dict) -> str:
        """Generate architecture overview based on detected tech stack."""
        overview_parts = []

        if tech_stack['has_python'] and tech_stack['has_nodejs']:
            overview_parts.append("Full-stack application with Python backend and Node.js frontend")
        elif tech_stack['has_python']:
            overview_parts.append("Python-based application")
        elif tech_stack['has_nodejs']:
            overview_parts.append("Node.js-based application")

        if tech_stack['has_celery']:
            overview_parts.append("Uses Celery for asynchronous task processing")

        if tech_stack['needs_redis']:
            overview_parts.append("Requires Redis for caching/message queuing")

        return ". ".join(overview_parts) if overview_parts else "Application architecture"
