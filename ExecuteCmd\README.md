# ExecuteCmd Microservice

## Overview

The ExecuteCmd microservice is an intelligent Docker-based execution engine that takes setup chains from the RunRepo service and executes them in isolated Docker containers. It uses AI agents to handle errors, fix issues automatically, and ensure successful application deployment.

## Features

- **AI-Powered Execution**: Uses Gemini AI to intelligently execute commands and handle errors
- **Docker Isolation**: Runs each setup chain in a clean Docker container environment
- **Automatic Error Recovery**: Detects and fixes common setup issues automatically
- **Real-time Monitoring**: Provides live updates on execution progress
- **Smart Problem Solving**: Analyzes error logs and applies appropriate fixes
- **Multi-Stack Support**: Handles Python, Node.js, and other technology stacks

## Architecture

```
ExecuteCmd/
├── app.py                 # Flask API server
├── docker_agent.py       # AI agent for Docker execution
├── error_handler.py      # Error detection and recovery
├── execution_monitor.py  # Real-time execution monitoring
├── requirements.txt      # Python dependencies
├── Dockerfile            # Container configuration
└── README.md             # This file
```

## API Endpoints

### POST /execute-setup-chain
Executes a setup chain in a Docker container.

**Request Body:**
```json
{
  "repository_url": "https://github.com/user/repo",
  "setup_chain": [...],
  "execution_options": {
    "base_image": "ubuntu:22.04",
    "timeout": 1800,
    "auto_fix": true
  }
}
```

**Response:**
```json
{
  "execution_id": "exec_123456",
  "status": "running",
  "container_id": "docker_container_id",
  "progress": {
    "current_step": 1,
    "total_steps": 5,
    "step_status": "executing"
  }
}
```

### GET /execution/{execution_id}/status
Get the current status of an execution.

### GET /execution/{execution_id}/logs
Get real-time logs from the execution.

### POST /execution/{execution_id}/stop
Stop a running execution.

## Environment Variables

- `GEMINI_API_KEY`: Google Gemini API key for AI capabilities
- `DOCKER_HOST`: Docker daemon host (default: unix:///var/run/docker.sock)
- `PORT`: Service port (default: 8005)
- `LOG_LEVEL`: Logging level (default: INFO)

## Usage

1. Start the service:
   ```bash
   python app.py
   ```

2. Send a setup chain for execution:
   ```bash
   curl -X POST http://localhost:8005/execute-setup-chain \
     -H "Content-Type: application/json" \
     -d @setup_chain.json
   ```

3. Monitor execution progress:
   ```bash
   curl http://localhost:8005/execution/{execution_id}/status
   ```

## Docker Requirements

- Docker daemon must be running and accessible
- Service needs access to Docker socket or Docker API
- Sufficient disk space for container images and builds

## Error Handling

The service automatically handles common issues:
- Missing dependencies
- Port conflicts
- Permission errors
- Network connectivity issues
- Build failures
- Configuration problems

## Security Considerations

- Containers run in isolated environments
- No privileged access by default
- Resource limits applied to prevent abuse
- Temporary containers are cleaned up automatically
