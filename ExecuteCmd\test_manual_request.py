#!/usr/bin/env python3
"""
Manual test for ExecuteCmd service using the exact request from RunRepo.
This bypasses the frontend chain and tests ExecuteCmd directly.
"""

import requests
import json
import time
import sys

def test_manual_execution():
    """Test ExecuteCmd with the exact request payload."""
    base_url = "http://localhost:8005"
    
    print("🧪 Manual ExecuteCmd Test")
    print("Testing with HR-Management setup chain from RunRepo")
    print("=" * 60)
    
    # The exact payload from your request
    payload = {
        "repository_url": "https://github.com/AliSoua/HR-Management",
        "setup_chain": [
            {
                "commands": [
                    "Check Python version: `python3 --version` (should be 3.9 or higher)",
                    "Check Git version: `git --version`",
                    "Check MySQL client version: `mysql --version` (or ensure MySQL server is installed and running)"
                ],
                "description": "Ensure you have Git, Python 3.9+ (recommended), and MySQL server/client installed on your system. These are fundamental tools required before setting up the application.",
                "estimated_time": "5-15 minutes (if not already installed)",
                "notes": [
                    "For macOS, consider Homebrew (`brew install python@3.9 git mysql`).",
                    "For Windows, use official installers or WSL (Windows Subsystem for Linux).",
                    "For Linux (Debian/Ubuntu), use `sudo apt update && sudo apt install python3 python3-pip git mysql-server`."
                ],
                "prerequisites": [],
                "step_number": 1,
                "title": "Install Prerequisites",
                "working_directory": "~/"
            },
            {
                "commands": [
                    "git clone https://github.com/AliSoua/HR-Management.git",
                    "cd HR-Management"
                ],
                "description": "Download the project files from GitHub to your local machine.",
                "estimated_time": "1-2 minutes",
                "notes": [],
                "prerequisites": ["Git installed"],
                "step_number": 2,
                "title": "Clone the Repository",
                "working_directory": "~/"
            },
            {
                "commands": [
                    "python3 -m venv venv",
                    "source venv/bin/activate"
                ],
                "description": "Create a dedicated virtual environment for the project to manage dependencies and avoid conflicts with other Python projects. This is a best practice for Python development.",
                "estimated_time": "1 minute",
                "notes": [
                    "On Windows, use `.\\venv\\Scripts\\activate` instead of `source venv/bin/activate`.",
                    "You will need to activate this environment in every new terminal session for the project."
                ],
                "prerequisites": ["Python installed"],
                "step_number": 3,
                "title": "Set Up Python Virtual Environment",
                "working_directory": "./HR-Management"
            },
            {
                "commands": [
                    "echo \"flask\" > requirements.txt",
                    "echo \"google-generativeai\" >> requirements.txt",
                    "echo \"python-dotenv\" >> requirements.txt",
                    "echo \"mysql-connector-python\" >> requirements.txt",
                    "pip install -r requirements.txt"
                ],
                "description": "The repository is missing a `requirements.txt` file. We will create one based on the `app.py` imports and then install all necessary Python libraries within the virtual environment.",
                "estimated_time": "2-5 minutes",
                "notes": ["Consider pinning specific versions in `requirements.txt` for production stability (e.g., `flask==2.3.3`)."],
                "prerequisites": ["Virtual environment activated"],
                "step_number": 4,
                "title": "Create `requirements.txt` and Install Python Dependencies",
                "working_directory": "./HR-Management"
            },
            {
                "commands": [
                    "echo \"-- Create the database if it doesn't exist\" > schema.sql",
                    "echo \"CREATE DATABASE IF NOT EXISTS hrm_db;\" >> schema.sql",
                    "echo \"\" >> schema.sql",
                    "echo \"-- Use the HR Management database\" >> schema.sql",
                    "echo \"USE hrm_db;\" >> schema.sql",
                    "echo \"\" >> schema.sql",
                    "echo \"-- Drop tables if they exist to ensure a clean slate\" >> schema.sql",
                    "echo \"DROP TABLE IF EXISTS leave_requests;\" >> schema.sql",
                    "echo \"DROP TABLE IF EXISTS payments;\" >> schema.sql",
                    "echo \"DROP TABLE IF EXISTS employees;\" >> schema.sql",
                    "echo \"DROP TABLE IF EXISTS departments;\" >> schema.sql",
                    "echo \"\" >> schema.sql",
                    "echo \"-- Create departments table\" >> schema.sql",
                    "echo \"CREATE TABLE departments (\" >> schema.sql",
                    "echo \"    id INT AUTO_INCREMENT PRIMARY KEY,\" >> schema.sql",
                    "echo \"    name VARCHAR(50) NOT NULL UNIQUE,\" >> schema.sql",
                    "echo \"    location VARCHAR(100)\" >> schema.sql",
                    "echo \");\" >> schema.sql",
                    "mysql -u root -p < schema.sql"
                ],
                "description": "Configure the MySQL database required by the application. This involves creating the database, a dedicated user, and populating the schema (tables) as expected by `app.py`. A `schema.sql` file will be created for this purpose.",
                "estimated_time": "3-7 minutes",
                "notes": [
                    "Replace `your_mysql_root_password` with your actual MySQL root password.",
                    "It's highly recommended to create a dedicated MySQL user for the application instead of using `root` in a production environment."
                ],
                "prerequisites": ["MySQL server installed and running", "Root or admin access to MySQL"],
                "step_number": 5,
                "title": "Set Up MySQL Database",
                "working_directory": "./HR-Management"
            }
        ],
        "execution_options": {
            "base_image": "ubuntu:22.04",
            "timeout": 1800,
            "auto_fix": True,
            "max_retries_per_step": 3
        }
    }
    
    print(f"📋 Setup Chain Details:")
    print(f"   Repository: {payload['repository_url']}")
    print(f"   Steps: {len(payload['setup_chain'])}")
    print(f"   Base Image: {payload['execution_options']['base_image']}")
    print(f"   Auto-fix: {payload['execution_options']['auto_fix']}")
    
    # Test health first
    print(f"\n1. Testing service health...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Service is healthy")
            print(f"   Docker available: {health_data.get('docker_available', False)}")
            print(f"   AI available: {health_data.get('ai_available', False)}")
            
            if not health_data.get('docker_available', False):
                print(f"⚠️ Docker not available - will run in simulation mode")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # Start execution
    print(f"\n2. Starting execution...")
    try:
        response = requests.post(
            f"{base_url}/execute-setup-chain",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            execution_id = result["execution_id"]
            print(f"✅ Execution started: {execution_id}")
            print(f"📊 Status: {result['status']}")
            print(f"🐳 Container: {result.get('container_id', 'N/A')}")
            
            # Monitor execution
            return monitor_execution(base_url, execution_id)
        else:
            print(f"❌ Failed to start execution: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error starting execution: {e}")
        return False

def monitor_execution(base_url: str, execution_id: str) -> bool:
    """Monitor execution progress with detailed output."""
    print(f"\n3. Monitoring execution {execution_id}...")
    print("=" * 60)
    
    start_time = time.time()
    last_step = 0
    
    while True:
        try:
            response = requests.get(f"{base_url}/execution/{execution_id}/status")
            
            if response.status_code == 200:
                status_data = response.json()
                status = status_data["status"]
                progress = status_data["progress"]
                current_step = progress['current_step']
                
                elapsed = int(time.time() - start_time)
                print(f"[{elapsed:03d}s] Status: {status} | Step: {current_step}/{progress['total_steps']} ({progress['percentage']}%)")
                
                # Show new step details
                if current_step > last_step and "steps" in status_data:
                    for step in status_data["steps"]:
                        if step["step_number"] == current_step:
                            print(f"  🔄 Executing: {step['title']}")
                            if step.get("retry_count", 0) > 0:
                                print(f"     🔄 Retried {step['retry_count']} times")
                            break
                    last_step = current_step
                
                # Show any new fixes applied
                if status_data.get("fixes_applied"):
                    recent_fixes = status_data["fixes_applied"][-1:]  # Show only latest fix
                    for fix in recent_fixes:
                        print(f"  🔧 Applied AI fix: {fix}")
                
                # Show step status
                if "steps" in status_data:
                    for step in status_data["steps"]:
                        step_status = step["status"]
                        step_icon = "✅" if step_status == "success" else "❌" if step_status == "failed" else "🔄" if step_status == "running" else "🔧" if step_status == "error_recovery" else "⏳"
                        if step["step_number"] <= current_step:
                            print(f"     {step_icon} Step {step['step_number']}: {step['title']} ({step_status})")
                
                # Check if completed
                if status in ["success", "failed", "stopped"]:
                    elapsed = int(time.time() - start_time)
                    print(f"\n🏁 Execution completed in {elapsed}s with status: {status}")
                    
                    if status == "success":
                        print("✅ Setup chain executed successfully!")
                        show_execution_summary(base_url, execution_id)
                        return True
                    else:
                        print(f"❌ Execution failed: {status_data.get('error_message', 'Unknown error')}")
                        show_execution_summary(base_url, execution_id)
                        return False
                
                time.sleep(3)  # Check every 3 seconds
                
            else:
                print(f"❌ Error getting status: {response.status_code}")
                return False
                
        except KeyboardInterrupt:
            print("\n⏹️ Monitoring interrupted by user")
            print(f"Execution {execution_id} may still be running")
            return False
        except Exception as e:
            print(f"❌ Error monitoring execution: {e}")
            time.sleep(5)

def show_execution_summary(base_url: str, execution_id: str):
    """Show detailed execution summary."""
    try:
        # Get detailed execution info
        response = requests.get(f"{base_url}/execution/{execution_id}/details")
        
        if response.status_code == 200:
            details = response.json()
            
            print(f"\n📋 Execution Summary:")
            print(f"   Execution ID: {execution_id}")
            print(f"   Total Steps: {details['total_steps']}")
            print(f"   Current Step: {details['current_step']}")
            print(f"   Status: {details['status']}")
            
            if details.get('fixes_applied'):
                print(f"   AI Fixes Applied: {len(details['fixes_applied'])}")
                for fix in details['fixes_applied']:
                    print(f"     - {fix}")
            
            # Show recent logs
            logs = details.get('logs', [])
            if logs:
                print(f"\n📋 Recent Logs (last 10):")
                print("-" * 50)
                for log in logs[-10:]:
                    print(f"   {log}")
                print("-" * 50)
                
        else:
            print(f"❌ Error getting execution details: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error getting execution summary: {e}")

def main():
    """Main test function."""
    print("🚀 Manual ExecuteCmd Test")
    print("This test uses the exact setup chain from RunRepo")
    print("=" * 60)
    
    success = test_manual_execution()
    
    if success:
        print("\n🎉 Manual test completed successfully!")
        print("The HR-Management setup chain was executed successfully.")
    else:
        print("\n💥 Manual test failed!")
        print("Check the ExecuteCmd service logs for details.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
