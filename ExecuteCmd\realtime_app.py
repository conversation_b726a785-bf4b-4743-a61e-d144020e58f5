#!/usr/bin/env python3
"""
Real-time ExecuteCmd Flask application with WebSocket support.
Provides step-by-step execution with real-time updates via WebSockets.
"""

import os
import logging
import uuid
import sys
import json
import asyncio
import threading
from flask import Flask, request, jsonify, g, has_request_context
from flask_cors import CORS
from typing import Dict, Any
import time

from realtime_executor import RealtimeExecutor
from websocket_server import start_websocket_server, broadcast_execution_update, get_websocket_server

# --- Logging Setup ---
logger = logging.getLogger("RealtimeExecuteCmdService")
logger.setLevel(logging.INFO)

class RequestIdFilter(logging.Filter):
    def filter(self, record):
        if has_request_context():
            record.request_id = g.get('request_id', 'N/A')
        else:
            record.request_id = 'startup'
        return True

# Create console handler with formatting
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - [%(request_id)s] - %(message)s'
)
console_handler.setFormatter(formatter)
console_handler.addFilter(RequestIdFilter())

# Add handler to logger
logger.addHandler(console_handler)

# --- Flask App Setup ---
app = Flask(__name__)
CORS(app)

# Global execution tracking
active_executions: Dict[str, RealtimeExecutor] = {}
execution_lock = threading.Lock()

# Start WebSocket server
websocket_thread = start_websocket_server()
logger.info("🚀 WebSocket server started for real-time communication")

@app.before_request
def start_request_logging():
    """Generate a unique ID for each request and log its start."""
    g.request_id = str(uuid.uuid4().hex[:12])
    logger.info(f"Incoming request: {request.method} {request.path} from {request.remote_addr}")

@app.route('/execute-setup-chain-realtime', methods=['POST'])
def execute_setup_chain_realtime():
    """
    Execute a setup chain with real-time WebSocket updates.
    Each step completion sends immediate updates to connected clients.
    """
    logger.info("Received request for real-time setup chain execution.")
    
    if not request.is_json:
        logger.warning("Request failed: payload is not JSON.")
        return jsonify({"error": "Request must be JSON"}), 400

    data = request.get_json()
    
    # Validate required fields
    repository_url = data.get('repository_url')
    setup_chain = data.get('setup_chain')
    
    if not repository_url:
        logger.warning("Request failed: missing repository_url.")
        return jsonify({"error": "Missing required field: repository_url"}), 400
    
    if not setup_chain or not isinstance(setup_chain, list):
        logger.warning("Request failed: setup_chain must be a non-empty list.")
        return jsonify({"error": "Missing or invalid field: setup_chain must be a list"}), 400

    try:
        # Generate execution ID
        execution_id = f"exec_{uuid.uuid4().hex[:12]}"
        
        # Create WebSocket callback for real-time updates
        async def websocket_callback(update_data):
            await broadcast_execution_update(execution_id, update_data)
        
        # Create real-time executor
        executor = RealtimeExecutor(
            execution_id=execution_id,
            repository_url=repository_url,
            setup_chain=setup_chain,
            websocket_callback=websocket_callback
        )
        
        # Store executor for tracking
        with execution_lock:
            active_executions[execution_id] = executor
        
        # Start execution in background
        def run_execution():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(executor.execute_setup_chain())
        
        execution_thread = threading.Thread(target=run_execution, daemon=True)
        execution_thread.start()
        
        response = {
            "execution_id": execution_id,
            "status": "started",
            "repository_url": repository_url,
            "total_steps": len(setup_chain),
            "websocket_url": f"ws://localhost:8006/execution/{execution_id}",
            "message": "Execution started. Connect to WebSocket for real-time updates."
        }
        
        logger.info(f"Started real-time execution {execution_id} for {repository_url}")
        return jsonify(response)

    except Exception as e:
        logger.error(f"An unexpected error occurred during execution start: {e}", exc_info=True)
        return jsonify({"error": "Internal server error during execution start"}), 500

@app.route('/execution/<execution_id>/summary', methods=['GET'])
def get_execution_summary(execution_id: str):
    """Get execution summary without polling."""
    logger.info(f"Summary request for execution {execution_id}")
    
    with execution_lock:
        executor = active_executions.get(execution_id)
    
    if not executor:
        logger.warning(f"Execution {execution_id} not found.")
        return jsonify({"error": "Execution not found"}), 404
    
    try:
        summary = executor.get_execution_summary()
        return jsonify(summary)

    except Exception as e:
        logger.error(f"Error getting summary for execution {execution_id}: {e}", exc_info=True)
        return jsonify({"error": "Internal server error"}), 500

@app.route('/executions/active', methods=['GET'])
def list_active_executions():
    """List all active executions."""
    logger.info("Request to list active executions")
    
    try:
        with execution_lock:
            executions_info = []
            for exec_id, executor in active_executions.items():
                summary = executor.get_execution_summary()
                executions_info.append({
                    "execution_id": exec_id,
                    "repository_url": summary["repository_url"],
                    "total_steps": summary["total_steps"],
                    "completed_steps": summary["completed_steps"],
                    "failed_steps": summary["failed_steps"],
                    "use_docker": summary["use_docker"]
                })
        
        response = {
            "executions": executions_info,
            "total_count": len(executions_info)
        }
        
        return jsonify(response)

    except Exception as e:
        logger.error(f"Error listing executions: {e}", exc_info=True)
        return jsonify({"error": "Internal server error"}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for the real-time service."""
    logger.debug("Health check endpoint was hit.")

    try:
        # Check Docker availability
        import docker
        import platform

        docker_available = False
        try:
            if platform.system() == 'Windows':
                # Try Windows Docker connections
                for base_url in ['npipe:////./pipe/docker_engine', 'tcp://localhost:2375']:
                    try:
                        client = docker.DockerClient(base_url=base_url)
                        client.ping()
                        docker_available = True
                        break
                    except:
                        continue
            else:
                client = docker.from_env()
                client.ping()
                docker_available = True
        except:
            pass

        # Check AI availability
        ai_available = bool(os.getenv('GEMINI_API_KEY'))
        
        # Check WebSocket server
        websocket_server = get_websocket_server()
        websocket_running = websocket_server.running

        health_status = {
            "service": "RealtimeExecuteCmdService",
            "status": "healthy",
            "docker_available": docker_available,
            "ai_available": ai_available,
            "websocket_running": websocket_running,
            "active_executions": len(active_executions),
            "websocket_clients": len(websocket_server.connected_clients),
            "timestamp": time.time()
        }

        return jsonify(health_status), 200

    except Exception as e:
        logger.error(f"Health check error: {e}")
        return jsonify({
            "service": "RealtimeExecuteCmdService",
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/websocket/info', methods=['GET'])
def websocket_info():
    """Get WebSocket connection information."""
    websocket_server = get_websocket_server()
    
    return jsonify({
        "websocket_url": f"ws://{websocket_server.host}:{websocket_server.port}",
        "connected_clients": len(websocket_server.connected_clients),
        "execution_subscribers": {
            exec_id: len(subscribers) 
            for exec_id, subscribers in websocket_server.execution_subscribers.items()
        }
    })

# Background cleanup task
def background_cleanup():
    """Background task to periodically clean up old executions."""
    while True:
        try:
            time.sleep(300)  # Run every 5 minutes

            with execution_lock:
                # Keep only the last 20 executions
                if len(active_executions) > 20:
                    oldest_executions = list(active_executions.keys())[:-20]
                    for exec_id in oldest_executions:
                        del active_executions[exec_id]
                        logger.info(f"Auto-cleaned old execution: {exec_id}")

        except Exception as e:
            logger.error(f"Error in background cleanup: {e}")

# Start background cleanup thread
cleanup_thread = threading.Thread(target=background_cleanup, daemon=True)
cleanup_thread.start()

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8007))  # Different port from original
    debug_mode = os.environ.get('FLASK_ENV') == 'development'

    if debug_mode:
        logger.setLevel(logging.DEBUG)
        logger.info("Service starting in DEBUG mode.")

    logger.info(f"Real-time ExecuteCmd service starting on http://0.0.0.0:{port}")
    logger.info("WebSocket server running on ws://localhost:8006")
    logger.info("Real-time step-by-step execution with AI error recovery ready")
    app.run(host='0.0.0.0', port=port, debug=debug_mode)
