#!/usr/bin/env python3
"""
Test script for RunRepo microservice integration.
This script tests the RunRepo service with sample data similar to what would come from ReadFiles.
"""

import requests
import json
import sys

# Configuration
RUNREPO_URL = "http://localhost:8004"

def test_runrepo_service():
    """Test the RunRepo service with sample analyzed files data."""
    
    # Sample data that would typically come from ReadFiles service
    test_data = {
        "repository_url": "https://github.com/AliSoua/Mailer-Service",
        "files_analyzed": [
            {
                "path": "README.md",
                "content": "# Mailer Microservice - Backend\n\nThis backend service provides an API endpoint to receive email sending requests...",
                "analysis": "This README.md file describes the Mailer Microservice Backend, a Python Flask application that handles email sending requests asynchronously.",
                "reason": "Provides top-level instructions for setting up and running the entire application."
            },
            {
                "path": "backend/requirements.txt",
                "content": "Flask\nCelery\nRedis\ngoogle-generativeai\npython-dotenv\nWaitress",
                "analysis": "This requirements.txt file lists the Python dependencies needed for the backend service.",
                "reason": "Lists all Python dependencies required to install and run the backend service."
            },
            {
                "path": "backend/app.py",
                "content": "from flask import Flask, request, jsonify\nfrom celery import Celery\n\napp = Flask(__name__)\n\<EMAIL>('/message', methods=['POST'])\ndef send_message():\n    # Email sending logic\n    pass",
                "analysis": "This is the main Flask application file that defines the API endpoints for the email service.",
                "reason": "The main entry point for the backend web application, typically defining routes and initializing the server."
            },
            {
                "path": "backend/run_worker.py",
                "content": "from celery import Celery\n\ncelery_app = Celery('mailer')\n\nif __name__ == '__main__':\n    celery_app.start()",
                "analysis": "This file is used to start the Celery worker process for handling background email tasks.",
                "reason": "The entry point for running background workers or asynchronous tasks, essential if the application has long-running processes."
            },
            {
                "path": "frontend/package.json",
                "content": "{\n  \"name\": \"mailer-frontend\",\n  \"version\": \"1.0.0\",\n  \"scripts\": {\n    \"start\": \"npm run dev\",\n    \"dev\": \"vite\",\n    \"build\": \"vite build\"\n  },\n  \"dependencies\": {\n    \"react\": \"^18.0.0\",\n    \"vite\": \"^4.0.0\"\n  }\n}",
                "analysis": "This package.json file defines the frontend React application with Vite as the build tool.",
                "reason": "Defines project metadata, scripts for building/running, and lists all Node.js dependencies for the frontend service."
            }
        ]
    }
    
    print("🚀 Testing RunRepo Microservice")
    print("=" * 50)
    
    # Test health endpoint
    print("\n1. Testing health endpoint...")
    try:
        response = requests.get(f"{RUNREPO_URL}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            print(f"   Response: {response.json()}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to RunRepo service. Make sure it's running on port 8004.")
        return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # Test setup chain generation with AI
    print("\n2. Testing setup chain generation (AI mode)...")
    try:
        response = requests.post(
            f"{RUNREPO_URL}/generate-setup-chain?use_ai=true",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ AI setup chain generation successful")
            print(f"   Generation method: {result.get('generation_method', 'Unknown')}")
            print(f"   AI enabled: {result.get('ai_enabled', 'Unknown')}")
            print(f"   Setup steps: {len(result.get('setup_chain', []))}")
            print(f"   Total time estimate: {result.get('total_estimated_time', 'Unknown')}")
            
            # Print first step as example
            if result.get('setup_chain'):
                first_step = result['setup_chain'][0]
                print(f"   First step: {first_step.get('title', 'Unknown')}")
        else:
            print(f"❌ AI setup chain generation failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ AI setup chain generation error: {e}")
        return False
    
    # Test setup chain generation without AI
    print("\n3. Testing setup chain generation (Template mode)...")
    try:
        response = requests.post(
            f"{RUNREPO_URL}/generate-setup-chain?use_ai=false",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Template setup chain generation successful")
            print(f"   Generation method: {result.get('generation_method', 'Unknown')}")
            print(f"   AI enabled: {result.get('ai_enabled', 'Unknown')}")
            print(f"   Setup steps: {len(result.get('setup_chain', []))}")
            print(f"   Total time estimate: {result.get('total_estimated_time', 'Unknown')}")
        else:
            print(f"❌ Template setup chain generation failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Template setup chain generation error: {e}")
        return False
    
    # Test error handling
    print("\n4. Testing error handling...")
    try:
        # Test with missing repository_url
        invalid_data = {"files_analyzed": []}
        response = requests.post(
            f"{RUNREPO_URL}/generate-setup-chain",
            json=invalid_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 400:
            print("✅ Error handling works correctly")
            print(f"   Error response: {response.json().get('error', 'Unknown')}")
        else:
            print(f"❌ Expected 400 error, got: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All RunRepo tests passed!")
    return True

if __name__ == "__main__":
    success = test_runrepo_service()
    sys.exit(0 if success else 1)
