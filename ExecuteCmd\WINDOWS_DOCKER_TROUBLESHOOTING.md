# Windows Docker Troubleshooting Guide

## The Issue

You're encountering this error:
```
AttributeError: module 'socket' has no attribute 'AF_UNIX'
```

This happens because the Docker Python library is trying to use Unix sockets, which aren't available on Windows.

## Solutions

### Option 1: Use Simulation Mode (Immediate Fix)

The ExecuteCmd service now includes a simulation mode that works without Docker:

1. **Test simulation mode**:
   ```bash
   cd ExecuteCmd
   python test_simulation_mode.py
   ```

2. **The service automatically enables simulation mode** when Docker is not available.

### Option 2: Fix Docker on Windows

#### Step 1: Install Docker Desktop
1. Download Docker Desktop from: https://www.docker.com/products/docker-desktop
2. Install and restart your computer
3. Start Docker Desktop

#### Step 2: Configure Docker Desktop
1. Open Docker Desktop
2. Go to Settings (gear icon)
3. Go to "General" tab
4. **Enable**: "Expose daemon on tcp://localhost:2375 without TLS"
5. Click "Apply & Restart"

#### Step 3: Test Docker Connectivity
```bash
cd ExecuteCmd
python test_docker_windows.py
```

#### Step 4: Verify Docker CLI
```bash
docker version
docker run hello-world
```

### Option 3: Alternative Docker Configurations

If Option 2 doesn't work, try these alternatives:

#### A. Use Windows Containers Mode
1. Right-click Docker Desktop system tray icon
2. Select "Switch to Windows containers..."
3. Wait for Docker to restart

#### B. Enable Hyper-V (if using Windows Pro/Enterprise)
1. Open "Turn Windows features on or off"
2. Enable "Hyper-V"
3. Restart computer

#### C. Use WSL 2 Backend (Windows 10/11)
1. Install WSL 2: `wsl --install`
2. In Docker Desktop settings:
   - Go to "General"
   - Enable "Use the WSL 2 based engine"
   - Apply & Restart

## Testing Your Setup

### 1. Test Docker Connectivity
```bash
python ExecuteCmd/test_docker_windows.py
```

### 2. Test ExecuteCmd Service
```bash
# Start the service
cd ExecuteCmd
python app.py

# In another terminal, test it
python test_simulation_mode.py
```

### 3. Test Full Integration
```bash
# Start all services, then test the frontend
cd frontend
npm run dev
# Go to http://localhost:5173
```

## Common Issues and Fixes

### Issue: "Docker Desktop is not running"
**Fix**: Start Docker Desktop and wait for it to fully load (green icon in system tray)

### Issue: "Access denied" or permission errors
**Fix**: 
1. Run PowerShell/Command Prompt as Administrator
2. Add your user to the "docker-users" group:
   ```cmd
   net localgroup docker-users "your-username" /add
   ```
3. Log out and log back in

### Issue: "Port 2375 connection refused"
**Fix**: 
1. Enable TCP daemon in Docker Desktop settings
2. Restart Docker Desktop
3. Check Windows Firewall isn't blocking port 2375

### Issue: "TLS handshake timeout"
**Fix**: 
1. Disable TLS for local development
2. Use the non-TLS port (2375) instead of TLS port (2376)

### Issue: "Container creation fails"
**Fix**:
1. Pull the base image manually: `docker pull ubuntu:22.04`
2. Check available disk space
3. Try a smaller base image: `alpine:latest`

## Service Modes

### Simulation Mode (No Docker Required)
- ✅ Tests the complete ExecuteCmd workflow
- ✅ Shows realistic command outputs
- ✅ Demonstrates AI error recovery
- ❌ Doesn't actually run commands
- ❌ Can't test real Docker issues

### Docker Mode (Full Functionality)
- ✅ Actually executes commands in containers
- ✅ Real isolation and security
- ✅ Tests actual deployment scenarios
- ❌ Requires Docker to be properly configured

## Recommended Approach

1. **Start with simulation mode** to test the service functionality
2. **Fix Docker connectivity** for full functionality
3. **Use the troubleshooting tools** provided to diagnose issues

## Support Commands

```bash
# Check Docker status
docker version
docker info

# Test Docker connectivity
python ExecuteCmd/test_docker_windows.py

# Test ExecuteCmd in simulation mode
python ExecuteCmd/test_simulation_mode.py

# Check ExecuteCmd service health
curl http://localhost:8005/health

# View ExecuteCmd logs
# Check the terminal where you started the service
```

## Getting Help

If you're still having issues:

1. **Check the logs** in the ExecuteCmd service terminal
2. **Run the diagnostic scripts** provided
3. **Try simulation mode** to verify the service logic works
4. **Check Docker Desktop logs** in the Docker Desktop application

The ExecuteCmd service is designed to be resilient and will automatically fall back to simulation mode when Docker is not available, so you can still test the complete workflow!
