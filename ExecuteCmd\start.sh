#!/bin/bash

# ExecuteCmd Microservice Startup Script

echo "🚀 Starting ExecuteCmd Microservice..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️ .env file not found. Creating from template..."
    cp .env.example .env
    echo "📝 Please edit .env file with your actual configuration values."
    echo "   Especially set your GEMINI_API_KEY for AI error recovery."
fi

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# Check if GEMINI_API_KEY is set
if [ -z "$GEMINI_API_KEY" ] || [ "$GEMINI_API_KEY" = "your_gemini_api_key_here" ]; then
    echo "⚠️ GEMINI_API_KEY is not set. AI error recovery will be disabled."
    echo "   Get your API key from: https://makersuite.google.com/app/apikey"
fi

# Create logs directory
mkdir -p logs

# Install Python dependencies if needed
if [ ! -d "venv" ]; then
    echo "📦 Creating Python virtual environment..."
    python3 -m venv venv
fi

echo "📦 Installing/updating dependencies..."
source venv/bin/activate
pip install -r requirements.txt

# Start the service
echo "🌟 Starting ExecuteCmd service on port ${PORT:-8005}..."
python app.py
