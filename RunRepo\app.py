# RunRepo Microservice - app.py
import os
import logging
import uuid
import sys
from flask import Flask, request, jsonify, g, has_request_context
from flask_cors import CORS
from setup_chain_agent import SetupChainAgent

# --- Logging Setup ---
logger = logging.getLogger("RunRepoService")
logger.setLevel(logging.INFO)

class RequestIdFilter(logging.Filter):
    def filter(self, record):
        if has_request_context():
            record.request_id = g.get('request_id', 'N/A')
        else:
            record.request_id = 'startup'
        return True

# Create console handler with formatting
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - [%(request_id)s] - %(message)s'
)
console_handler.setFormatter(formatter)
console_handler.addFilter(RequestIdFilter())

# Add handler to logger
logger.addHandler(console_handler)

# --- Flask App Setup ---
app = Flask(__name__)
CORS(app)

@app.before_request
def start_request_logging():
    """Generate a unique ID for each request and log its start."""
    g.request_id = str(uuid.uuid4().hex[:12])
    logger.info(f"Incoming request: {request.method} {request.path} from {request.remote_addr}")

@app.route('/generate-setup-chain', methods=['POST'])
def generate_setup_chain():
    """
    Receives analyzed files from ReadFiles service and generates a comprehensive
    setup chain with commands to run the application.
    """
    logger.info("Received request to generate setup chain.")
    
    if not request.is_json:
        logger.warning("Request failed: payload is not JSON.")
        return jsonify({"error": "Request must be JSON"}), 400

    data = request.get_json()
    
    # Validate required fields
    repository_url = data.get('repository_url')
    files_analyzed = data.get('files_analyzed')
    
    if not repository_url:
        logger.warning("Request failed: missing repository_url.")
        return jsonify({"error": "Missing required field: repository_url"}), 400
    
    if not files_analyzed or not isinstance(files_analyzed, list):
        logger.warning("Request failed: files_analyzed must be a non-empty list.")
        return jsonify({"error": "Missing or invalid field: files_analyzed must be a list"}), 400

    try:
        # Check if AI should be used
        use_ai_param = request.args.get('use_ai', 'true').lower() == 'true'
        
        agent = SetupChainAgent(repository_url, files_analyzed, use_ai=use_ai_param)
        
        if use_ai_param and not agent.gemini_model:
            logger.warning("AI requested but not configured.")
            return jsonify({"error": "AI Agent is not configured. Check GEMINI_API_KEY."}), 503

        result = agent.generate_setup_chain()
        
        # Add metadata about the generation method used
        result["generation_method"] = "AI-Enhanced" if agent.gemini_model else "Template-Based"
        result["ai_enabled"] = agent.gemini_model is not None
        result["repository_url"] = repository_url
        
        logger.info(f"Successfully generated setup chain for {repository_url}")
        return jsonify(result)

    except Exception as e:
        logger.error(f"An unexpected error occurred during setup chain generation: {e}", exc_info=True)
        return jsonify({"error": "Internal server error during setup chain generation"}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for the service."""
    logger.debug("Health check endpoint was hit.")
    return jsonify({"service": "RunRepoService", "status": "healthy"})

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8004)) 
    debug_mode = os.environ.get('FLASK_ENV') == 'development'

    if debug_mode:
        logger.setLevel(logging.DEBUG)
        logger.info("Service starting in DEBUG mode.")
    
    logger.info(f"RunRepo service starting on http://0.0.0.0:{port}")
    app.run(host='0.0.0.0', port=port, debug=debug_mode)
