import { useState, useEffect, useRef } from 'react';
import './App.css';

const CRAWLER_API_BASE_URL = 'http://localhost:8000';
const ANALYZER_API_BASE_URL = 'http://localhost:8001';
const READFILES_API_BASE_URL = 'http://localhost:8003';
const RUNREPO_API_BASE_URL = 'http://localhost:8004';
const EXECUTECMD_API_BASE_URL = 'http://localhost:8005';

function App() {
  const [repoUrl, setRepoUrl] = useState('');
  const [jobId, setJobId] = useState(null);
  const [jobStatus, setJobStatus] = useState(null);
  const [jobResult, setJobResult] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  
  // New state for the analysis step
  const [analysisStatus, setAnalysisStatus] = useState(null); // e.g., 'identifying', 'completed', 'failed'
  const [analysisResult, setAnalysisResult] = useState(null);
  const [useAI, setUseAI] = useState(true); // Toggle for AI vs rule-based analysis

  // New state for file reading step
  const [fileReadingStatus, setFileReadingStatus] = useState(null); // e.g., 'reading', 'completed', 'failed'
  const [fileReadingResult, setFileReadingResult] = useState(null);

  // New state for setup chain generation step
  const [setupChainStatus, setSetupChainStatus] = useState(null); // e.g., 'generating', 'completed', 'failed'
  const [setupChainResult, setSetupChainResult] = useState(null);

  // New state for execution step
  const [executionStatus, setExecutionStatus] = useState(null); // e.g., 'starting', 'running', 'completed', 'failed'
  const [executionResult, setExecutionResult] = useState(null);
  const [executionId, setExecutionId] = useState(null);
  const [executionLogs, setExecutionLogs] = useState([]);
  const [executionProgress, setExecutionProgress] = useState(null);
  
  const pollingInterval = useRef(null);
  const executionPollingInterval = useRef(null);

  // Helper function to render analysis results in a user-friendly way
  const renderAnalysisResult = (result) => {
    if (typeof result === 'string') {
      return <pre>{result}</pre>;
    }

    if (!result || !result.files_to_read) {
      return <pre>{JSON.stringify(result, null, 2)}</pre>;
    }

    const { files_to_read, analysis_method, ai_enabled } = result;

    return (
      <div className="analysis-results">
        <div className="analysis-metadata">
          <div className="analysis-method">
            <strong>Analysis Method:</strong> {analysis_method}
            {ai_enabled ? ' 🤖' : ' 📋'}
          </div>
          <div className="files-count">
            <strong>Files Selected:</strong> {files_to_read.length}
          </div>
        </div>

        <div className="files-list">
          <h3>Recommended Files to Analyze:</h3>
          {files_to_read.map((file, index) => (
            <div key={index} className="file-item">
              <div className="file-path">
                <code>{file.path}</code>
              </div>
              <div className="file-reason">
                {file.reason}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Helper function to render file reading results
  const renderFileReadingResult = (result) => {
    if (typeof result === 'string') {
      return <pre>{result}</pre>;
    }

    if (!result || !result.files_analyzed) {
      return <pre>{JSON.stringify(result, null, 2)}</pre>;
    }

    const { files_analyzed, summary, analysis_method, ai_enabled, errors } = result;

    return (
      <div className="file-reading-results">
        <div className="reading-metadata">
          <div className="analysis-method">
            <strong>Reading Method:</strong> {analysis_method}
            {ai_enabled ? ' 🤖' : ' 📄'}
          </div>
          <div className="files-count">
            <strong>Files Read:</strong> {files_analyzed.length}
          </div>
          {errors && errors.length > 0 && (
            <div className="errors-count">
              <strong>Errors:</strong> {errors.length}
            </div>
          )}
        </div>

        {summary && (
          <div className="overall-summary">
            <h3>📋 Repository Summary</h3>
            <div className="summary-content">
              <pre>{summary}</pre>
            </div>
          </div>
        )}

        <div className="files-content">
          <h3>📁 File Contents & Analysis</h3>
          {files_analyzed.map((file, index) => (
            <div key={index} className="file-content-item">
              <div className="file-header">
                <h4>
                  <code>{file.path}</code>
                  {file.error && <span className="error-badge">❌ Error</span>}
                </h4>
                <p className="file-reason">{file.reason}</p>
              </div>

              {file.error ? (
                <div className="file-error">
                  <strong>Error:</strong> {file.error}
                </div>
              ) : (
                <>
                  {file.analysis && (
                    <div className="file-analysis">
                      <h5>🤖 AI Analysis:</h5>
                      <div className="analysis-content">
                        <pre>{file.analysis}</pre>
                      </div>
                    </div>
                  )}

                  {file.content && (
                    <div className="file-content">
                      <h5>📄 File Content:</h5>
                      <div className="content-preview">
                        <pre>{file.content.length > 2000 ?
                          file.content.substring(0, 2000) + '\n\n... (content truncated)' :
                          file.content}
                        </pre>
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          ))}
        </div>

        {errors && errors.length > 0 && (
          <div className="errors-section">
            <h3>⚠️ Errors</h3>
            <ul>
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  };

  // Helper function to render setup chain results
  const renderSetupChainResult = (result) => {
    if (typeof result === 'string') {
      return <pre>{result}</pre>;
    }

    if (!result || !result.setup_chain) {
      return <pre>{JSON.stringify(result, null, 2)}</pre>;
    }

    const {
      setup_chain,
      summary,
      total_estimated_time,
      architecture_overview,
      important_notes,
      troubleshooting,
      generation_method,
      ai_enabled
    } = result;

    return (
      <div className="setup-chain-results">
        <div className="setup-metadata">
          <div className="generation-method">
            <strong>Generation Method:</strong> {generation_method}
            {ai_enabled ? ' 🤖' : ' 📋'}
          </div>
          <div className="total-time">
            <strong>Total Estimated Time:</strong> {total_estimated_time}
          </div>
          <div className="steps-count">
            <strong>Setup Steps:</strong> {setup_chain.length}
          </div>
        </div>

        {summary && (
          <div className="setup-summary">
            <h3>📋 Setup Summary</h3>
            <p>{summary}</p>
          </div>
        )}

        {architecture_overview && (
          <div className="architecture-overview">
            <h3>🏗️ Architecture Overview</h3>
            <p>{architecture_overview}</p>
          </div>
        )}

        <div className="setup-steps">
          <h3>🚀 Setup Steps</h3>
          {setup_chain.map((step, index) => (
            <div key={index} className="setup-step">
              <div className="step-header">
                <h4>
                  Step {step.step_number}: {step.title}
                  <span className="estimated-time">⏱️ {step.estimated_time}</span>
                </h4>
                <p className="step-description">{step.description}</p>
              </div>

              <div className="step-content">
                <div className="step-section">
                  <h5>📁 Working Directory:</h5>
                  <code>{step.working_directory}</code>
                </div>

                {step.prerequisites && step.prerequisites.length > 0 && (
                  <div className="step-section">
                    <h5>✅ Prerequisites:</h5>
                    <ul>
                      {step.prerequisites.map((prereq, i) => (
                        <li key={i}>{prereq}</li>
                      ))}
                    </ul>
                  </div>
                )}

                <div className="step-section">
                  <h5>💻 Commands:</h5>
                  <div className="commands-list">
                    {step.commands.map((command, i) => (
                      <div key={i} className="command-item">
                        <code>{command}</code>
                      </div>
                    ))}
                  </div>
                </div>

                {step.notes && step.notes.length > 0 && (
                  <div className="step-section">
                    <h5>📝 Notes:</h5>
                    <ul>
                      {step.notes.map((note, i) => (
                        <li key={i}>{note}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {important_notes && important_notes.length > 0 && (
          <div className="important-notes">
            <h3>⚠️ Important Notes</h3>
            <ul>
              {important_notes.map((note, index) => (
                <li key={index}>{note}</li>
              ))}
            </ul>
          </div>
        )}

        {troubleshooting && troubleshooting.length > 0 && (
          <div className="troubleshooting">
            <h3>🔧 Troubleshooting</h3>
            {troubleshooting.map((item, index) => (
              <div key={index} className="troubleshooting-item">
                <h5>Issue: {item.issue}</h5>
                <p><strong>Solution:</strong> {item.solution}</p>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  // Helper function to render execution results
  const renderExecutionResult = (result, progress, logs) => {
    if (typeof result === 'string') {
      return <pre>{result}</pre>;
    }

    if (!result) {
      return <div>Starting execution...</div>;
    }

    const getStatusIcon = (status) => {
      switch (status) {
        case 'success': return '✅';
        case 'failed': return '❌';
        case 'running': return '🔄';
        case 'stopped': return '⏹️';
        case 'error_recovery': return '🔧';
        default: return '⏳';
      }
    };

    return (
      <div className="execution-results">
        <div className="execution-metadata">
          <div className="execution-id">
            <strong>Execution ID:</strong> {result.execution_id || executionId}
          </div>
          <div className="container-id">
            <strong>Container:</strong> {result.container_id || 'N/A'}
          </div>
          {result.status && (
            <div className="execution-status">
              <strong>Status:</strong> {getStatusIcon(result.status)} {result.status}
            </div>
          )}
        </div>

        {progress && (
          <div className="execution-progress">
            <h3>📊 Progress</h3>
            <div className="progress-bar">
              <div
                className="progress-fill"
                style={{ width: `${progress.percentage || 0}%` }}
              ></div>
            </div>
            <div className="progress-text">
              Step {progress.current_step || 0} of {progress.total_steps || 0}
              ({progress.percentage || 0}%)
            </div>
          </div>
        )}

        {result.steps && result.steps.length > 0 && (
          <div className="execution-steps">
            <h3>🚀 Execution Steps</h3>
            {result.steps.map((step, index) => (
              <div key={index} className={`execution-step ${step.status}`}>
                <div className="step-header">
                  <h4>
                    {getStatusIcon(step.status)} Step {step.step_number}: {step.title}
                    {step.retry_count > 0 && (
                      <span className="retry-count">🔄 Retried {step.retry_count}x</span>
                    )}
                  </h4>
                </div>
                {step.has_error && step.error_preview && (
                  <div className="step-error">
                    <strong>Error:</strong> {step.error_preview}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {result.fixes_applied && result.fixes_applied.length > 0 && (
          <div className="fixes-applied">
            <h3>🔧 AI Fixes Applied</h3>
            <ul>
              {result.fixes_applied.map((fix, index) => (
                <li key={index}>{fix}</li>
              ))}
            </ul>
          </div>
        )}

        {logs && logs.length > 0 && (
          <div className="execution-logs">
            <h3>📋 Execution Logs</h3>
            <div className="logs-container">
              {logs.slice(-20).map((log, index) => (
                <div key={index} className="log-entry">
                  {log}
                </div>
              ))}
            </div>
          </div>
        )}

        {result.error_message && (
          <div className="execution-error">
            <h3>❌ Error</h3>
            <p>{result.error_message}</p>
          </div>
        )}
      </div>
    );
  };

  // Effect for polling the job status
  useEffect(() => {
    if (jobId && (jobStatus === 'running' || jobStatus === 'pending')) {
      pollingInterval.current = setInterval(checkCrawlStatus, 3000);
    }
    return () => clearInterval(pollingInterval.current);
  }, [jobId, jobStatus]);

  // Effect for polling execution status
  useEffect(() => {
    if (executionId && (executionStatus === 'running' || executionStatus === 'starting')) {
      executionPollingInterval.current = setInterval(checkExecutionStatus, 2000);
    }
    return () => clearInterval(executionPollingInterval.current);
  }, [executionId, executionStatus]);


  const handleCrawlRequest = async (e) => {
    e.preventDefault();
    if (isLoading) return;
    
    resetState();
    setIsLoading(true);
    setJobStatus('creating');

    try {
      const response = await fetch(`${CRAWLER_API_BASE_URL}/crawl`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url: repoUrl }),
      });

      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Failed to create job.');

      setJobId(data.job_id);
      setJobStatus('running');
      setJobResult(`Job created successfully! Polling for status... (ID: ${data.job_id})`);

    } catch (error) {
      setJobStatus('failed');
      setJobResult(`Creation Error: ${error.message}`);
      setIsLoading(false);
    }
  };

  const checkCrawlStatus = async () => {
    if (!jobId) return;

    try {
      const response = await fetch(`${CRAWLER_API_BASE_URL}/crawl/status/${jobId}`);
      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Failed to get status.');
      
      if (data.status === 'completed') {
        clearInterval(pollingInterval.current);
        setJobStatus(data.status);
        setJobResult(data.result);
        setIsLoading(false);
        // --- THIS IS THE NEW PART ---
        // Automatically trigger the analysis step
        handleAnalysisRequest(data); 
      } else if (data.status === 'failed') {
        clearInterval(pollingInterval.current);
        setJobStatus(data.status);
        setJobResult(`Job Failed: ${data.error}`);
        setIsLoading(false);
      } else {
        setJobStatus(data.status);
      }
    } catch (error) {
      clearInterval(pollingInterval.current);
      setJobStatus('failed');
      setJobResult(`Polling Error: ${error.message}`);
      setIsLoading(false);
    }
  };

  // --- NEW FUNCTION TO HANDLE ANALYSIS ---
  const handleAnalysisRequest = async (crawlData) => {
    setAnalysisStatus('identifying');
    setAnalysisResult(`Identifying critical files using ${useAI ? 'AI-powered' : 'rule-based'} analysis...`);

    try {
      const response = await fetch(`${ANALYZER_API_BASE_URL}/identify-files?use_ai=${useAI}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(crawlData) // Send the full crawl result
      });

      const analysisData = await response.json();
      if (!response.ok) throw new Error(analysisData.error || 'File identification failed.');

      setAnalysisStatus('completed');
      setAnalysisResult(analysisData);

      // --- AUTOMATICALLY TRIGGER FILE READING AFTER ANALYSIS COMPLETES ---
      if (analysisData.files_to_read && analysisData.files_to_read.length > 0) {
        await handleFileReadingRequest(analysisData, repoUrl);
      }

    } catch (error) {
      setAnalysisStatus('failed');
      setAnalysisResult(`Analysis Error: ${error.message}`);
    }
  };

  // --- NEW FUNCTION TO HANDLE FILE READING ---
  const handleFileReadingRequest = async (analysisData, repositoryUrl) => {
    setFileReadingStatus('reading');
    setFileReadingResult(`Reading and analyzing ${analysisData.files_to_read.length} files using ${useAI ? 'AI-powered' : 'basic'} analysis...`);

    try {
      const response = await fetch(`${READFILES_API_BASE_URL}/read-files?use_ai=${useAI}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          repository_url: repositoryUrl,
          files_to_read: analysisData.files_to_read
        })
      });

      const fileData = await response.json();
      if (!response.ok) throw new Error(fileData.error || 'File reading failed.');

      setFileReadingStatus('completed');
      setFileReadingResult(fileData);

      // --- AUTOMATICALLY TRIGGER SETUP CHAIN GENERATION AFTER FILE READING COMPLETES ---
      if (fileData.files_analyzed && fileData.files_analyzed.length > 0) {
        await handleSetupChainRequest(fileData, repositoryUrl);
      }

    } catch (error) {
      setFileReadingStatus('failed');
      setFileReadingResult(`File Reading Error: ${error.message}`);
    }
  };

  // --- NEW FUNCTION TO HANDLE SETUP CHAIN GENERATION ---
  const handleSetupChainRequest = async (fileData, repositoryUrl) => {
    setSetupChainStatus('generating');
    setSetupChainResult(`Generating setup chain for ${repositoryUrl} using ${useAI ? 'AI-powered' : 'template-based'} analysis...`);

    try {
      const response = await fetch(`${RUNREPO_API_BASE_URL}/generate-setup-chain?use_ai=${useAI}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          repository_url: repositoryUrl,
          files_analyzed: fileData.files_analyzed
        })
      });

      const setupData = await response.json();
      if (!response.ok) throw new Error(setupData.error || 'Setup chain generation failed.');

      setSetupChainStatus('completed');
      setSetupChainResult(setupData);

      // --- AUTOMATICALLY TRIGGER EXECUTION AFTER SETUP CHAIN GENERATION COMPLETES ---
      if (setupData.setup_chain && setupData.setup_chain.length > 0) {
        await handleExecutionRequest(setupData, repositoryUrl);
      }

    } catch (error) {
      setSetupChainStatus('failed');
      setSetupChainResult(`Setup Chain Generation Error: ${error.message}`);
    }
  };

  // --- NEW FUNCTION TO HANDLE EXECUTION ---
  const handleExecutionRequest = async (setupData, repositoryUrl) => {
    setExecutionStatus('starting');
    setExecutionResult(`Starting execution of setup chain for ${repositoryUrl}...`);
    setExecutionLogs([]);
    setExecutionProgress(null);

    try {
      const response = await fetch(`${EXECUTECMD_API_BASE_URL}/execute-setup-chain`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          repository_url: repositoryUrl,
          setup_chain: setupData.setup_chain,
          execution_options: {
            base_image: 'ubuntu:22.04',
            timeout: 1800,
            auto_fix: true,
            max_retries_per_step: 3
          }
        })
      });

      const executionData = await response.json();
      if (!response.ok) throw new Error(executionData.error || 'Execution failed to start.');

      setExecutionId(executionData.execution_id);
      setExecutionStatus('running');
      setExecutionResult(executionData);
      setExecutionProgress(executionData.progress);

    } catch (error) {
      setExecutionStatus('failed');
      setExecutionResult(`Execution Error: ${error.message}`);
    }
  };

  // --- NEW FUNCTION TO CHECK EXECUTION STATUS ---
  const checkExecutionStatus = async () => {
    if (!executionId) return;

    try {
      // Get status
      const statusResponse = await fetch(`${EXECUTECMD_API_BASE_URL}/execution/${executionId}/status`);
      const statusData = await statusResponse.json();

      if (!statusResponse.ok) throw new Error(statusData.error || 'Failed to get execution status.');

      setExecutionResult(statusData);
      setExecutionProgress(statusData.progress);

      // Get logs
      const logsResponse = await fetch(`${EXECUTECMD_API_BASE_URL}/execution/${executionId}/logs`);
      if (logsResponse.ok) {
        const logsData = await logsResponse.json();
        setExecutionLogs(logsData.logs || []);
      }

      // Check if execution completed
      if (statusData.status === 'success') {
        clearInterval(executionPollingInterval.current);
        setExecutionStatus('completed');
      } else if (statusData.status === 'failed' || statusData.status === 'stopped') {
        clearInterval(executionPollingInterval.current);
        setExecutionStatus('failed');
      }

    } catch (error) {
      clearInterval(executionPollingInterval.current);
      setExecutionStatus('failed');
      setExecutionResult(`Execution Polling Error: ${error.message}`);
    }
  };

  // --- NEW FUNCTION TO STOP EXECUTION ---
  const handleStopExecution = async () => {
    if (!executionId) return;

    try {
      const response = await fetch(`${EXECUTECMD_API_BASE_URL}/execution/${executionId}/stop`, {
        method: 'POST'
      });

      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Failed to stop execution.');

      clearInterval(executionPollingInterval.current);
      setExecutionStatus('stopped');
      alert('Execution stopped successfully');

    } catch (error) {
      alert(`Stop Execution Error: ${error.message}`);
    }
  };

  const handleCloseJob = async () => {
    if (!jobId) return;
    setIsLoading(true);
    clearInterval(pollingInterval.current);

    try {
      const response = await fetch(`${CRAWLER_API_BASE_URL}/crawl/jobs/${jobId}`, { method: 'DELETE' });
      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Failed to close job.');
      resetState();
      alert(data.message);
    } catch (error) {
      alert(`Close Error: ${error.message}`);
      setIsLoading(false);
    }
  };
  
  const resetState = () => {
    clearInterval(pollingInterval.current);
    clearInterval(executionPollingInterval.current);
    setJobId(null);
    setJobStatus(null);
    setJobResult(null);
    setIsLoading(false);
    // Reset analysis state as well
    setAnalysisStatus(null);
    setAnalysisResult(null);
    // Reset file reading state as well
    setFileReadingStatus(null);
    setFileReadingResult(null);
    // Reset setup chain state as well
    setSetupChainStatus(null);
    setSetupChainResult(null);
    // Reset execution state as well
    setExecutionStatus(null);
    setExecutionResult(null);
    setExecutionId(null);
    setExecutionLogs([]);
    setExecutionProgress(null);
    // Keep AI setting - don't reset it
  };

  return (
    <main className="container">
      <h1>GitCrawl and Analyze</h1>
      <form onSubmit={handleCrawlRequest}>
        <input
          type="url"
          value={repoUrl}
          onChange={(e) => setRepoUrl(e.target.value)}
          placeholder="Enter GitHub Repository URL"
          required
          disabled={isLoading}
        />
        <button type="submit" disabled={isLoading}>
          {isLoading ? 'Processing...' : 'Crawl'}
        </button>
      </form>

      {/* AI Analysis Toggle */}
      <div className="analysis-settings">
        <label className="toggle-container">
          <input
            type="checkbox"
            checked={useAI}
            onChange={(e) => setUseAI(e.target.checked)}
            disabled={isLoading}
          />
          <span className="toggle-label">
            Use AI-powered file analysis {useAI ? '🤖' : '📋'}
          </span>
        </label>
        <p className="toggle-description">
          {useAI
            ? 'AI will intelligently select the most important files to analyze based on project structure and content.'
            : 'Use simple rule-based pattern matching to identify common configuration files.'
          }
        </p>
      </div>

      {jobStatus && (
        <div className={`status-box ${jobStatus}`}>
          <div className="status-header">
            <strong>Crawl Status: {jobStatus.toUpperCase()}</strong>
            {jobId && (
              <button onClick={handleCloseJob} disabled={isLoading} className="close-btn">
                Close Job
              </button>
            )}
          </div>
          <div className="result-area">
            <pre>
              {typeof jobResult === 'object' && jobResult !== null
                ? JSON.stringify(jobResult, null, 2)
                : jobResult}
            </pre>
          </div>
        </div>
      )}
      
      {/* --- ENHANCED UI FOR ANALYSIS RESULT --- */}
      {analysisStatus && (
        <div className={`analysis-box ${analysisStatus}`}>
          <div className="status-header">
            <strong>File Analysis: {analysisStatus.toUpperCase()}</strong>
          </div>
          <div className="result-area">
            {renderAnalysisResult(analysisResult)}
          </div>
        </div>
      )}

      {/* --- NEW UI FOR FILE READING RESULTS --- */}
      {fileReadingStatus && (
        <div className={`file-reading-box ${fileReadingStatus}`}>
          <div className="status-header">
            <strong>File Reading & Analysis: {fileReadingStatus.toUpperCase()}</strong>
          </div>
          <div className="result-area">
            {renderFileReadingResult(fileReadingResult)}
          </div>
        </div>
      )}

      {/* --- NEW UI FOR SETUP CHAIN RESULTS --- */}
      {setupChainStatus && (
        <div className={`setup-chain-box ${setupChainStatus}`}>
          <div className="status-header">
            <strong>Setup Chain Generation: {setupChainStatus.toUpperCase()}</strong>
          </div>
          <div className="result-area">
            {renderSetupChainResult(setupChainResult)}
          </div>
        </div>
      )}

      {/* --- NEW UI FOR EXECUTION RESULTS --- */}
      {executionStatus && (
        <div className={`execution-box ${executionStatus}`}>
          <div className="status-header">
            <strong>🐳 Docker Execution: {executionStatus.toUpperCase()}</strong>
            {executionId && (executionStatus === 'running' || executionStatus === 'starting') && (
              <button onClick={handleStopExecution} className="stop-btn">
                ⏹️ Stop Execution
              </button>
            )}
          </div>
          <div className="result-area">
            {renderExecutionResult(executionResult, executionProgress, executionLogs)}
          </div>
        </div>
      )}
    </main>
  );
}

export default App;