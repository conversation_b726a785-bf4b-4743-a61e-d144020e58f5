#!/usr/bin/env python3
"""
Real-time execution engine with intelligent step-by-step processing.
Uses WebSockets for real-time communication with frontend.
"""

import os
import logging
import json
import time
import asyncio
import websockets
import threading
from typing import Dict, List, Any, Optional, Callable
import google.generativeai as genai
from dataclasses import dataclass, asdict
from enum import Enum
import docker
import subprocess
import platform

logger = logging.getLogger("RealtimeExecutor")

class StepStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    ANALYZING = "analyzing"
    FIXING = "fixing"

@dataclass
class StepResult:
    step_number: int
    title: str
    status: StepStatus
    command: str = ""
    output: str = ""
    error: str = ""
    fix_applied: str = ""
    duration: float = 0.0
    timestamp: str = ""

@dataclass
class ExecutionUpdate:
    execution_id: str
    current_step: int
    total_steps: int
    status: str
    step_result: Optional[StepResult] = None
    message: str = ""
    progress_percentage: float = 0.0

class RealtimeExecutor:
    """
    Intelligent step-by-step executor with real-time WebSocket communication.
    """
    
    def __init__(self, execution_id: str, repository_url: str, setup_chain: List[Dict], 
                 websocket_callback: Callable = None):
        self.execution_id = execution_id
        self.repository_url = repository_url
        self.setup_chain = setup_chain
        self.websocket_callback = websocket_callback
        
        # AI model for error analysis
        self.gemini_model = None
        self._initialize_gemini()
        
        # Docker client (with fallback)
        self.docker_client = None
        self.use_docker = self._initialize_docker()
        
        # Execution state
        self.current_step = 0
        self.total_steps = len(setup_chain)
        self.step_results: List[StepResult] = []
        self.working_directory = "/workspace" if self.use_docker else os.getcwd()
        self.container = None
        
        # Real-time communication
        self.connected_clients = set()
    
    def _initialize_gemini(self):
        """Initialize Gemini AI for error analysis."""
        try:
            api_key = os.getenv('GEMINI_API_KEY')
            if api_key:
                genai.configure(api_key=api_key)
                self.gemini_model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')
                logger.info("✅ Gemini AI initialized")
            else:
                logger.warning("⚠️ GEMINI_API_KEY not found - AI error recovery disabled")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Gemini: {e}")
    
    def _initialize_docker(self) -> bool:
        """Initialize Docker client with fallback."""
        try:
            if platform.system() == 'Windows':
                # Try Windows Docker connections
                for base_url in ['npipe:////./pipe/docker_engine', 'tcp://localhost:2375']:
                    try:
                        client = docker.DockerClient(base_url=base_url)
                        client.ping()
                        self.docker_client = client
                        logger.info(f"✅ Docker connected via {base_url}")
                        return True
                    except:
                        continue
            else:
                # Unix/Linux Docker
                self.docker_client = docker.from_env()
                self.docker_client.ping()
                logger.info("✅ Docker connected")
                return True
        except Exception as e:
            logger.warning(f"⚠️ Docker not available: {e}")
        
        logger.info("🎭 Using local execution mode")
        return False
    
    async def execute_setup_chain(self):
        """Execute the setup chain step by step with real-time updates."""
        logger.info(f"🚀 Starting execution: {self.execution_id}")
        
        # Send initial update
        await self._send_update("Starting execution...", "running")
        
        try:
            # Setup execution environment
            if self.use_docker:
                await self._setup_docker_environment()
            else:
                await self._setup_local_environment()
            
            # Execute each step
            for i, step_data in enumerate(self.setup_chain):
                self.current_step = i + 1
                
                step_result = StepResult(
                    step_number=self.current_step,
                    title=step_data.get('title', f'Step {self.current_step}'),
                    status=StepStatus.PENDING,
                    timestamp=time.strftime("%H:%M:%S")
                )
                
                # Send step start update
                step_result.status = StepStatus.RUNNING
                await self._send_step_update(step_result, f"Executing: {step_result.title}")
                
                # Execute step commands
                success = await self._execute_step(step_data, step_result)
                
                if success:
                    step_result.status = StepStatus.SUCCESS
                    await self._send_step_update(step_result, f"✅ {step_result.title} completed")
                else:
                    step_result.status = StepStatus.FAILED
                    await self._send_step_update(step_result, f"❌ {step_result.title} failed")
                    break
                
                self.step_results.append(step_result)
            
            # Final status
            if all(r.status == StepStatus.SUCCESS for r in self.step_results):
                await self._send_update("🎉 All steps completed successfully!", "success")
            else:
                await self._send_update("💥 Execution failed", "failed")
                
        except Exception as e:
            logger.error(f"❌ Execution error: {e}")
            await self._send_update(f"💥 Execution error: {str(e)}", "failed")
        
        finally:
            await self._cleanup()
    
    async def _execute_step(self, step_data: Dict, step_result: StepResult) -> bool:
        """Execute a single step with intelligent error handling."""
        commands = step_data.get('commands', [])
        working_dir = step_data.get('working_directory', './')
        
        start_time = time.time()
        
        for command in commands:
            if not command.strip() or command.strip().startswith('#'):
                continue
            
            step_result.command = command
            
            # Execute command
            success, output, error = await self._execute_command(command, working_dir)
            
            step_result.output += output + "\n"
            
            if success:
                logger.info(f"✅ Command succeeded: {command}")
            else:
                logger.warning(f"❌ Command failed: {command}")
                step_result.error = error
                
                # Try AI-powered error recovery
                if self.gemini_model:
                    step_result.status = StepStatus.ANALYZING
                    await self._send_step_update(step_result, f"🤖 Analyzing error...")
                    
                    fix_success = await self._attempt_ai_fix(command, error, working_dir, step_result)
                    
                    if fix_success:
                        # Retry the original command
                        success, output, error = await self._execute_command(command, working_dir)
                        step_result.output += f"\n[RETRY] {output}"
                        
                        if success:
                            logger.info(f"✅ Command succeeded after fix: {command}")
                        else:
                            step_result.error += f"\n[RETRY FAILED] {error}"
                            step_result.duration = time.time() - start_time
                            return False
                    else:
                        step_result.duration = time.time() - start_time
                        return False
                else:
                    step_result.duration = time.time() - start_time
                    return False
        
        step_result.duration = time.time() - start_time
        return True
    
    async def _execute_command(self, command: str, working_dir: str) -> tuple[bool, str, str]:
        """Execute a command in the appropriate environment."""
        if self.use_docker and self.container:
            return await self._execute_docker_command(command, working_dir)
        else:
            return await self._execute_local_command(command, working_dir)
    
    async def _execute_docker_command(self, command: str, working_dir: str) -> tuple[bool, str, str]:
        """Execute command in Docker container."""
        try:
            # Resolve working directory
            if working_dir.startswith('./'):
                work_path = working_dir.replace('./', '/workspace/')
            elif working_dir.startswith('~/'):
                work_path = working_dir.replace('~/', '/workspace/')
            else:
                work_path = working_dir
            
            full_command = f"cd {work_path} && {command}"
            
            exec_result = self.container.exec_run(
                f'/bin/bash -c "{full_command}"',
                stdout=True,
                stderr=True
            )
            
            output = exec_result.output.decode('utf-8', errors='replace')
            success = exec_result.exit_code == 0
            
            return success, output, output if not success else ""
            
        except Exception as e:
            error_msg = f"Docker execution error: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg
    
    async def _execute_local_command(self, command: str, working_dir: str) -> tuple[bool, str, str]:
        """Execute command locally."""
        try:
            # Resolve working directory
            if working_dir.startswith('./'):
                work_path = os.path.join(self.working_directory, working_dir[2:])
            elif working_dir.startswith('~/'):
                work_path = os.path.join(self.working_directory, working_dir[2:])
            else:
                work_path = working_dir
            
            # Ensure directory exists
            os.makedirs(work_path, exist_ok=True)
            
            result = subprocess.run(
                command,
                shell=True,
                cwd=work_path,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            success = result.returncode == 0
            output = result.stdout + result.stderr
            error = result.stderr if not success else ""
            
            return success, output, error
            
        except subprocess.TimeoutExpired:
            return False, "", "Command timed out after 5 minutes"
        except Exception as e:
            error_msg = f"Local execution error: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg

    async def _attempt_ai_fix(self, failed_command: str, error: str, working_dir: str, step_result: StepResult) -> bool:
        """Use AI to analyze error and generate fix commands."""
        try:
            step_result.status = StepStatus.ANALYZING
            await self._send_step_update(step_result, "🤖 AI analyzing error...")

            prompt = f"""
You are an expert DevOps engineer. A command failed during setup. Analyze the error and provide fix commands.

FAILED COMMAND: {failed_command}
WORKING DIRECTORY: {working_dir}
ERROR OUTPUT: {error}

Provide a JSON response with:
{{
  "analysis": "Brief analysis of the problem",
  "fix_commands": ["command1", "command2"],
  "explanation": "What the fix does"
}}

Common fixes:
- Missing packages: Install with apt-get, yum, brew, etc.
- Missing tools: Install git, python3, mysql-client, etc.
- Permission issues: Use sudo, chmod, chown
- Directory issues: Create with mkdir -p
- Environment issues: Set PATH, install dependencies

Only provide commands that will fix the specific error. Be practical and specific.
"""

            response = await asyncio.get_event_loop().run_in_executor(
                None, self.gemini_model.generate_content, prompt
            )

            # Parse AI response
            try:
                ai_response = json.loads(response.text.strip())
                analysis = ai_response.get('analysis', 'AI analysis failed')
                fix_commands = ai_response.get('fix_commands', [])
                explanation = ai_response.get('explanation', 'No explanation provided')

                logger.info(f"🤖 AI Analysis: {analysis}")

                step_result.status = StepStatus.FIXING
                await self._send_step_update(step_result, f"🔧 Applying AI fix: {explanation}")

                # Execute fix commands
                for fix_cmd in fix_commands:
                    logger.info(f"🔧 Executing fix: {fix_cmd}")
                    success, output, fix_error = await self._execute_command(fix_cmd, working_dir)

                    step_result.output += f"\n[FIX] {fix_cmd}\n{output}"

                    if not success:
                        logger.warning(f"❌ Fix command failed: {fix_cmd}")
                        step_result.error += f"\n[FIX FAILED] {fix_error}"
                        return False

                step_result.fix_applied = explanation
                logger.info(f"✅ AI fix applied successfully")
                return True

            except json.JSONDecodeError:
                logger.error("❌ Failed to parse AI response as JSON")
                return False

        except Exception as e:
            logger.error(f"❌ AI fix attempt failed: {e}")
            return False

    async def _setup_docker_environment(self):
        """Setup Docker container for execution."""
        try:
            await self._send_update("🐳 Setting up Docker environment...", "running")

            # Create container
            self.container = self.docker_client.containers.run(
                'ubuntu:22.04',
                command='sleep infinity',
                detach=True,
                working_dir='/workspace',
                environment={'DEBIAN_FRONTEND': 'noninteractive'}
            )

            # Wait for container to be ready
            await asyncio.sleep(2)

            # Install basic tools
            await self._send_update("🔧 Installing basic tools...", "running")

            basic_setup_commands = [
                "apt-get update",
                "apt-get install -y git curl wget python3 python3-pip python3-venv mysql-client"
            ]

            for cmd in basic_setup_commands:
                success, output, error = await self._execute_docker_command(cmd, "/workspace")
                if not success:
                    logger.warning(f"⚠️ Basic setup command failed: {cmd}")

            await self._send_update("✅ Docker environment ready", "running")

        except Exception as e:
            logger.error(f"❌ Docker setup failed: {e}")
            raise

    async def _setup_local_environment(self):
        """Setup local execution environment."""
        await self._send_update("🏠 Setting up local environment...", "running")

        # Create workspace directory
        self.working_directory = os.path.join(os.getcwd(), "workspace")
        os.makedirs(self.working_directory, exist_ok=True)

        await self._send_update("✅ Local environment ready", "running")

    async def _send_update(self, message: str, status: str):
        """Send general execution update."""
        update = ExecutionUpdate(
            execution_id=self.execution_id,
            current_step=self.current_step,
            total_steps=self.total_steps,
            status=status,
            message=message,
            progress_percentage=(self.current_step / self.total_steps) * 100 if self.total_steps > 0 else 0
        )

        await self._broadcast_update(update)

    async def _send_step_update(self, step_result: StepResult, message: str):
        """Send step-specific update."""
        update = ExecutionUpdate(
            execution_id=self.execution_id,
            current_step=self.current_step,
            total_steps=self.total_steps,
            status="running",
            step_result=step_result,
            message=message,
            progress_percentage=(self.current_step / self.total_steps) * 100 if self.total_steps > 0 else 0
        )

        await self._broadcast_update(update)

    async def _broadcast_update(self, update: ExecutionUpdate):
        """Broadcast update to all connected WebSocket clients."""
        if self.websocket_callback:
            try:
                await self.websocket_callback(asdict(update))
            except Exception as e:
                logger.error(f"❌ WebSocket broadcast error: {e}")

        # Also log the update
        logger.info(f"📡 Update: {update.message}")

    async def _cleanup(self):
        """Cleanup resources."""
        try:
            if self.container:
                await self._send_update("🗑️ Cleaning up Docker container...", "running")
                self.container.remove(force=True)
                logger.info("✅ Docker container cleaned up")
        except Exception as e:
            logger.error(f"❌ Cleanup error: {e}")

    def get_execution_summary(self) -> Dict:
        """Get execution summary."""
        return {
            "execution_id": self.execution_id,
            "repository_url": self.repository_url,
            "total_steps": self.total_steps,
            "completed_steps": len([r for r in self.step_results if r.status == StepStatus.SUCCESS]),
            "failed_steps": len([r for r in self.step_results if r.status == StepStatus.FAILED]),
            "step_results": [asdict(r) for r in self.step_results],
            "use_docker": self.use_docker
        }
