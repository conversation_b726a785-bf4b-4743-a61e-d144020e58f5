version: '3.8'

services:
  executecmd:
    build: .
    ports:
      - "8005:8005"
    environment:
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - FLASK_ENV=development
      - LOG_LEVEL=INFO
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8005/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - executecmd-network

networks:
  executecmd-network:
    driver: bridge
