# RunRepo Microservice

A microservice that generates comprehensive setup chains for running applications based on analyzed repository files.

## Overview

The RunRepo microservice receives analyzed files from the ReadFiles service and generates a detailed, step-by-step setup chain that guides users through:
1. Cloning and setting up the repository
2. Installing all dependencies
3. Configuring the environment
4. Building the application (if needed)
5. Running the application successfully

## Features

- **AI-Powered Analysis**: Uses Gemini 2.0 Flash to intelligently analyze repository structure and generate optimal setup chains
- **Technology Stack Detection**: Automatically detects Python, Node.js, Celery, Redis, and other technologies
- **Comprehensive Instructions**: Provides detailed commands, prerequisites, and troubleshooting tips
- **Template Fallback**: Works without AI using template-based analysis
- **Time Estimates**: Includes estimated time for each setup step
- **Error Handling**: Robust error handling with detailed logging

## API Endpoints

### POST /generate-setup-chain

Generates a comprehensive setup chain for a repository.

**Query Parameters:**
- `use_ai` (optional): Set to "true" (default) for AI-enhanced analysis or "false" for template-based analysis

**Request Body:**
```json
{
  "repository_url": "https://github.com/owner/repo",
  "files_analyzed": [
    {
      "path": "README.md",
      "content": "file content...",
      "analysis": "AI analysis of the file...",
      "reason": "Main documentation file"
    },
    {
      "path": "package.json",
      "content": "file content...",
      "analysis": "AI analysis of the file...",
      "reason": "NPM dependencies and scripts"
    }
  ]
}
```

**Response:**
```json
{
  "setup_chain": [
    {
      "step_number": 1,
      "title": "Clone Repository",
      "description": "Clone the repository to your local machine",
      "commands": ["git clone https://github.com/owner/repo", "cd repo"],
      "working_directory": "./",
      "prerequisites": ["Git installed"],
      "notes": ["Make sure you have access to the repository"],
      "estimated_time": "1-2 minutes"
    }
  ],
  "summary": "Setup chain for Python application with 5 steps",
  "total_estimated_time": "15-20 minutes",
  "architecture_overview": "Python-based application with Celery for task processing",
  "important_notes": ["Follow steps in order", "Check prerequisites"],
  "troubleshooting": [
    {
      "issue": "Dependencies installation fails",
      "solution": "Check internet connection and package manager version"
    }
  ],
  "generation_method": "AI-Enhanced",
  "ai_enabled": true,
  "repository_url": "https://github.com/owner/repo"
}
```

### GET /health

Health check endpoint.

**Response:**
```json
{
  "service": "RunRepoService",
  "status": "healthy"
}
```

## Environment Variables

- `GEMINI_API_KEY`: Required for AI analysis functionality
- `PORT`: Service port (default: 8004)
- `FLASK_ENV`: Set to "development" for debug mode

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up environment variables:
```bash
export GEMINI_API_KEY="your-gemini-api-key"
export PORT=8004
```

3. Run the service:
```bash
python app.py
```

## Docker Support

You can also run this service in a Docker container. Make sure to pass the required environment variables.

## Integration with Other Services

This service is designed to work as part of the repository analysis pipeline:

1. **GitCrawl** → Crawls repository structure
2. **GitAnalyser** → Identifies important files
3. **ReadFiles** → Reads and analyzes file contents
4. **RunRepo** → Generates setup chain (this service)

## Technology Stack Detection

The service automatically detects and handles:

- **Python**: Virtual environments, pip, requirements.txt
- **Node.js**: npm/yarn, package.json
- **Celery**: Worker processes, Redis dependencies
- **Environment Files**: .env configuration
- **External Dependencies**: Redis, databases, etc.

## AI vs Template Mode

### AI Mode (Default)
- Uses Gemini AI to analyze file contents and generate intelligent setup chains
- Provides context-aware instructions
- Handles complex project structures
- Requires `GEMINI_API_KEY`

### Template Mode (Fallback)
- Uses rule-based analysis to detect technology stack
- Generates setup chains based on predefined templates
- Works without AI dependencies
- Provides basic but functional setup instructions

## Error Handling

The service includes comprehensive error handling:
- Invalid JSON requests
- Missing required fields
- AI service failures (falls back to template mode)
- Malformed file analysis data

## Logging

All operations are logged with unique request IDs for easy debugging and monitoring.
