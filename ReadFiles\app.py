# read-files/app.py
from flask import Flask, request, jsonify, g, has_request_context
from flask_cors import CORS
import os
import logging
import uuid
import sys
from file_reader_agent import FileReaderAgent

# --- Enhanced Logging Configuration ---
# 1. Create a logger with a specific name
logger = logging.getLogger("ReadFilesService")
logger.setLevel(logging.INFO) # Set the lowest level to capture

# 2. Create a handler to write logs to console (stdout)
handler = logging.StreamHandler(sys.stdout)

# 3. Create a formatter for structured logs
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - [RequestID: %(request_id)s] - %(message)s'
)
handler.setFormatter(formatter)

# 4. Add a filter to inject the request_id into every log record
class RequestIdFilter(logging.Filter):
    """
    A logging filter that injects the request ID from Flask's `g` object.
    It handles logs that occur outside of a request context.
    """
    def filter(self, record):
        if has_request_context():
            record.request_id = g.get('request_id', 'N/A')
        else:
            record.request_id = 'startup'
        return True

# 5. Add the filter and handler to the logger
if not logger.handlers:
    logger.addFilter(RequestIdFilter())
    logger.addHandler(handler)
# --- End of Logging Configuration ---

app = Flask(__name__)
CORS(app)

@app.before_request
def start_request_logging():
    """Generate a unique ID for each request and log its start."""
    g.request_id = str(uuid.uuid4().hex[:12])
    logger.info(f"Incoming request: {request.method} {request.path} from {request.remote_addr}")

@app.route('/read-files', methods=['POST'])
async def read_files(): # <<< This route is ASYNC
    """
    Receives a list of files to read and their repository URL,
    fetches the file contents, and analyzes them using Gemini concurrently with rate-limiting.
    """
    logger.info("Received request to read and analyze files.")

    if not request.is_json:
        logger.warning("Request failed: payload is not JSON.")
        return jsonify({"error": "Request must be JSON"}), 400

    data = request.get_json()
    logger.debug(f"Request data: {data}")
    
    files_to_read = data.get('files_to_read', [])
    repository_url = data.get('repository_url')
    
    if not files_to_read or not isinstance(files_to_read, list):
        logger.warning("Request failed: 'files_to_read' must be a non-empty list.")
        return jsonify({"error": "'files_to_read' must be a non-empty list"}), 400
    
    if not repository_url:
        logger.warning("Request failed: 'repository_url' is required.")
        return jsonify({"error": "'repository_url' is required"}), 400

    try:
        use_ai_param = request.args.get('use_ai', 'true').lower() == 'true'

        agent = FileReaderAgent(repository_url, use_ai=use_ai_param)
        
        # <<< AWAIT the asynchronous, rate-limited method from the agent
        result = await agent.read_and_analyze_files_async(files_to_read)

        result["analysis_method"] = "AI-Enhanced" if agent.gemini_model else "Basic-Fetch"
        result["ai_enabled"] = agent.gemini_model is not None

        return jsonify(result)

    except Exception as e:
        logger.error(f"An unexpected error occurred during file reading: {e}", exc_info=True)
        return jsonify({"error": "Internal server error during file analysis"}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for the service."""
    logger.debug("Health check endpoint was hit.")
    return jsonify({"service": "ReadFiles", "status": "healthy"})


if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8003)) 
    debug_mode = os.environ.get('FLASK_ENV') == 'development'

    if debug_mode:
        logger.setLevel(logging.DEBUG)
        logger.info("Service starting in DEBUG mode.")
    
    logger.info(f"ReadFiles service starting on http://0.0.0.0:{port}")
    app.run(host='0.0.0.0', port=port, debug=debug_mode)