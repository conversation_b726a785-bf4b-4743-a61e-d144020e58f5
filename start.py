#!/usr/bin/env python3
"""
Startup script to run all microservices for the repository analysis pipeline.
This script helps users start all services in the correct order.
"""

import subprocess
import sys
import time
import os
from pathlib import Path

# Service configurations
SERVICES = [
    {
        "name": "GitCrawl",
        "path": "GitCrawl",
        "port": 8000,
        "command": ["python", "app.py"],
        "description": "Repository crawling service"
    },
    {
        "name": "GitAnalyser", 
        "path": "GitAnalyser",
        "port": 8001,
        "command": ["python", "app.py"],
        "description": "File analysis and triage service"
    },
    {
        "name": "ComGen",
        "path": "ComGen", 
        "port": 8002,
        "command": ["python", "app.py"],
        "description": "Command generation service"
    },
    {
        "name": "ReadFiles",
        "path": "ReadFiles",
        "port": 8003, 
        "command": ["python", "app.py"],
        "description": "File reading and analysis service"
    },
    {
        "name": "RunRepo",
        "path": "RunRepo",
        "port": 8004,
        "command": ["python", "app.py"], 
        "description": "Setup chain generation service"
    },
    {
        "name": "Frontend",
        "path": "frontend",
        "port": 5173,
        "command": ["npm", "run", "dev"],
        "description": "React frontend application"
    }
]

def check_prerequisites():
    """Check if required tools are installed."""
    print("🔍 Checking prerequisites...")
    
    # Check Python
    try:
        result = subprocess.run(["python", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Python: {result.stdout.strip()}")
        else:
            print("❌ Python not found")
            return False
    except FileNotFoundError:
        print("❌ Python not found")
        return False
    
    # Check Node.js
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js: {result.stdout.strip()}")
        else:
            print("❌ Node.js not found")
            return False
    except FileNotFoundError:
        print("❌ Node.js not found")
        return False
    
    
    return True

def install_dependencies():
    """Install dependencies for all services."""
    print("\n📦 Installing dependencies...")
    
    # Install Python dependencies for each service
    python_services = [s for s in SERVICES if s["name"] != "Frontend"]
    for service in python_services:
        service_path = Path(service["path"])
        requirements_file = service_path / "requirements.txt"
        
        if requirements_file.exists():
            print(f"   Installing dependencies for {service['name']}...")
            try:
                subprocess.run(
                    ["pip", "install", "-r", str(requirements_file)],
                    cwd=service_path,
                    check=True,
                    capture_output=True
                )
                print(f"   ✅ {service['name']} dependencies installed")
            except subprocess.CalledProcessError as e:
                print(f"   ❌ Failed to install {service['name']} dependencies: {e}")
                return False
        else:
            print(f"   ⚠️  No requirements.txt found for {service['name']}")
    
    # Install Node.js dependencies for frontend
    frontend_path = Path("frontend")
    if frontend_path.exists():
        package_json = frontend_path / "package.json"
        if package_json.exists():
            print("   Installing frontend dependencies...")
            try:
                subprocess.run(
                    ["npm", "install"],
                    cwd=frontend_path,
                    check=True,
                    capture_output=True
                )
                print("   ✅ Frontend dependencies installed")
            except subprocess.CalledProcessError as e:
                print(f"   ❌ Failed to install frontend dependencies: {e}")
                return False
        else:
            print("   ⚠️  No package.json found for frontend")
    
    return True

def start_services():
    """Start all services."""
    print("\n🚀 Starting services...")
    print("=" * 60)
    
    processes = []
    
    for service in SERVICES:
        service_path = Path(service["path"])
        
        if not service_path.exists():
            print(f"⚠️  Service directory not found: {service['path']}")
            continue
        
        print(f"Starting {service['name']} on port {service['port']}...")
        print(f"   Description: {service['description']}")
        print(f"   Command: {' '.join(service['command'])}")
        print(f"   Working directory: {service_path}")
        
        try:
            # Start the service
            process = subprocess.Popen(
                service["command"],
                cwd=service_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            processes.append({
                "name": service["name"],
                "process": process,
                "port": service["port"]
            })
            
            print(f"   ✅ {service['name']} started (PID: {process.pid})")
            
            # Small delay between service starts
            time.sleep(2)
            
        except Exception as e:
            print(f"   ❌ Failed to start {service['name']}: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 All services started!")
    print("\nService URLs:")
    for service in SERVICES:
        if service["name"] == "Frontend":
            print(f"   {service['name']}: http://localhost:{service['port']}")
        else:
            print(f"   {service['name']}: http://localhost:{service['port']}")
    
    print("\n📝 Important Notes:")
    print("   - Make sure to set GEMINI_API_KEY environment variable for AI features")
    print("   - The frontend will automatically connect to all backend services")
    print("   - Check individual service logs if you encounter issues")
    print("   - Press Ctrl+C to stop all services")
    
    # Keep the script running and monitor processes
    try:
        while True:
            time.sleep(5)
            # Check if any process has died
            for proc_info in processes:
                if proc_info["process"].poll() is not None:
                    print(f"⚠️  {proc_info['name']} has stopped unexpectedly")
    except KeyboardInterrupt:
        print("\n\n🛑 Stopping all services...")
        for proc_info in processes:
            proc_info["process"].terminate()
        print("✅ All services stopped")

def main():
    """Main function."""
    print("🌟 Repository Analysis Pipeline Startup Script")
    print("=" * 60)
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites check failed. Please install missing tools.")
        sys.exit(1)
    
    # Ask user if they want to install dependencies
    install_deps = input("\n📦 Install/update dependencies? (y/N): ").lower().strip()
    if install_deps in ['y', 'yes']:
        if not install_dependencies():
            print("\n❌ Dependency installation failed.")
            sys.exit(1)
    
    # Start services
    start_services()

if __name__ == "__main__":
    main()
