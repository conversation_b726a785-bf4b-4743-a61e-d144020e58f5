# ExecuteCmd Microservice - app.py
import os
import logging
import uuid
import sys
import json
from flask import Flask, request, jsonify, g, has_request_context
from flask_cors import CORS
from docker_agent import DockerExecutionAgent, ExecutionStatus
from typing import Dict, Any
import threading
import time

# --- Logging Setup ---
logger = logging.getLogger("ExecuteCmdService")
logger.setLevel(logging.INFO)

class RequestIdFilter(logging.Filter):
    def filter(self, record):
        if has_request_context():
            record.request_id = g.get('request_id', 'N/A')
        else:
            record.request_id = 'startup'
        return True

# Create console handler with formatting
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - [%(request_id)s] - %(message)s'
)
console_handler.setFormatter(formatter)
console_handler.addFilter(RequestIdFilter())

# Add handler to logger
logger.addHandler(console_handler)

# --- Flask App Setup ---
app = Flask(__name__)
CORS(app)

# Global execution tracking
active_executions: Dict[str, DockerExecutionAgent] = {}
execution_lock = threading.Lock()

@app.before_request
def start_request_logging():
    """Generate a unique ID for each request and log its start."""
    g.request_id = str(uuid.uuid4().hex[:12])
    logger.info(f"Incoming request: {request.method} {request.path} from {request.remote_addr}")

@app.route('/execute-setup-chain', methods=['POST'])
def execute_setup_chain():
    """
    Execute a setup chain in a Docker container with AI-powered error handling.
    """
    logger.info("Received request to execute setup chain.")
    
    if not request.is_json:
        logger.warning("Request failed: payload is not JSON.")
        return jsonify({"error": "Request must be JSON"}), 400

    data = request.get_json()
    
    # Validate required fields
    repository_url = data.get('repository_url')
    setup_chain = data.get('setup_chain')
    
    if not repository_url:
        logger.warning("Request failed: missing repository_url.")
        return jsonify({"error": "Missing required field: repository_url"}), 400
    
    if not setup_chain or not isinstance(setup_chain, list):
        logger.warning("Request failed: setup_chain must be a non-empty list.")
        return jsonify({"error": "Missing or invalid field: setup_chain must be a list"}), 400

    try:
        # Generate execution ID
        execution_id = f"exec_{uuid.uuid4().hex[:12]}"
        
        # Get execution options
        execution_options = data.get('execution_options', {})
        
        # Default execution options
        default_options = {
            'base_image': 'ubuntu:22.04',
            'timeout': 1800,  # 30 minutes
            'auto_fix': True,
            'max_retries_per_step': 3
        }
        default_options.update(execution_options)
        
        # Create progress callback
        def progress_callback(result):
            logger.info(f"Execution {execution_id} progress: Step {result.current_step}/{result.total_steps}, Status: {result.status.value}")
        
        # Create Docker execution agent
        agent = DockerExecutionAgent(
            execution_id=execution_id,
            repository_url=repository_url,
            setup_chain=setup_chain,
            execution_options=default_options,
            progress_callback=progress_callback
        )
        
        # Store agent for tracking
        with execution_lock:
            active_executions[execution_id] = agent
        
        # Start execution
        agent.start_execution()
        
        # Get initial status
        status = agent.get_status()
        
        response = {
            "execution_id": execution_id,
            "status": status.status.value,
            "container_id": status.container_id,
            "progress": {
                "current_step": status.current_step,
                "total_steps": status.total_steps,
                "step_status": "starting"
            },
            "repository_url": repository_url,
            "execution_options": default_options
        }
        
        logger.info(f"Started execution {execution_id} for {repository_url}")
        return jsonify(response)

    except Exception as e:
        logger.error(f"An unexpected error occurred during execution start: {e}", exc_info=True)
        return jsonify({"error": "Internal server error during execution start"}), 500

@app.route('/execution/<execution_id>/status', methods=['GET'])
def get_execution_status(execution_id: str):
    """Get the current status of an execution."""
    logger.info(f"Status request for execution {execution_id}")
    
    with execution_lock:
        agent = active_executions.get(execution_id)
    
    if not agent:
        logger.warning(f"Execution {execution_id} not found.")
        return jsonify({"error": "Execution not found"}), 404
    
    try:
        status = agent.get_status()
        
        # Prepare step details
        steps_info = []
        for step in status.steps:
            steps_info.append({
                "step_number": step.step_number,
                "title": step.title,
                "status": step.status.value,
                "retry_count": step.retry_count,
                "has_error": bool(step.error),
                "error_preview": step.error[:200] + "..." if len(step.error) > 200 else step.error
            })
        
        response = {
            "execution_id": execution_id,
            "status": status.status.value,
            "container_id": status.container_id,
            "progress": {
                "current_step": status.current_step,
                "total_steps": status.total_steps,
                "percentage": round((status.current_step / status.total_steps) * 100, 1) if status.total_steps > 0 else 0
            },
            "steps": steps_info,
            "error_message": status.error_message,
            "fixes_applied": status.fixes_applied,
            "logs_count": len(status.logs)
        }
        
        return jsonify(response)

    except Exception as e:
        logger.error(f"Error getting status for execution {execution_id}: {e}", exc_info=True)
        return jsonify({"error": "Internal server error"}), 500

@app.route('/execution/<execution_id>/logs', methods=['GET'])
def get_execution_logs(execution_id: str):
    """Get logs from an execution."""
    logger.info(f"Logs request for execution {execution_id}")
    
    with execution_lock:
        agent = active_executions.get(execution_id)
    
    if not agent:
        logger.warning(f"Execution {execution_id} not found.")
        return jsonify({"error": "Execution not found"}), 404
    
    try:
        # Get query parameters
        start_line = request.args.get('start', 0, type=int)
        limit = request.args.get('limit', 100, type=int)
        
        status = agent.get_status()
        logs = status.logs[start_line:start_line + limit]
        
        response = {
            "execution_id": execution_id,
            "logs": logs,
            "total_logs": len(status.logs),
            "start_line": start_line,
            "returned_count": len(logs),
            "has_more": start_line + len(logs) < len(status.logs)
        }
        
        return jsonify(response)

    except Exception as e:
        logger.error(f"Error getting logs for execution {execution_id}: {e}", exc_info=True)
        return jsonify({"error": "Internal server error"}), 500

@app.route('/execution/<execution_id>/stop', methods=['POST'])
def stop_execution(execution_id: str):
    """Stop a running execution."""
    logger.info(f"Stop request for execution {execution_id}")
    
    with execution_lock:
        agent = active_executions.get(execution_id)
    
    if not agent:
        logger.warning(f"Execution {execution_id} not found.")
        return jsonify({"error": "Execution not found"}), 404
    
    try:
        agent.stop_execution()
        
        response = {
            "execution_id": execution_id,
            "message": "Execution stop requested",
            "status": "stopping"
        }
        
        logger.info(f"Stop requested for execution {execution_id}")
        return jsonify(response)

    except Exception as e:
        logger.error(f"Error stopping execution {execution_id}: {e}", exc_info=True)
        return jsonify({"error": "Internal server error"}), 500

@app.route('/executions', methods=['GET'])
def list_executions():
    """List all executions."""
    logger.info("Request to list all executions")
    
    try:
        with execution_lock:
            executions_info = []
            for exec_id, agent in active_executions.items():
                status = agent.get_status()
                executions_info.append({
                    "execution_id": exec_id,
                    "status": status.status.value,
                    "current_step": status.current_step,
                    "total_steps": status.total_steps,
                    "container_id": status.container_id
                })
        
        response = {
            "executions": executions_info,
            "total_count": len(executions_info)
        }
        
        return jsonify(response)

    except Exception as e:
        logger.error(f"Error listing executions: {e}", exc_info=True)
        return jsonify({"error": "Internal server error"}), 500

@app.route('/execution/<execution_id>/details', methods=['GET'])
def get_execution_details(execution_id: str):
    """Get detailed information about an execution including step details."""
    logger.info(f"Details request for execution {execution_id}")

    with execution_lock:
        agent = active_executions.get(execution_id)

    if not agent:
        logger.warning(f"Execution {execution_id} not found.")
        return jsonify({"error": "Execution not found"}), 404

    try:
        status = agent.get_status()

        # Prepare detailed step information
        steps_details = []
        for step in status.steps:
            steps_details.append({
                "step_number": step.step_number,
                "title": step.title,
                "status": step.status.value,
                "commands": step.commands,
                "working_directory": step.working_directory,
                "retry_count": step.retry_count,
                "max_retries": step.max_retries,
                "output": step.output,
                "error": step.error
            })

        response = {
            "execution_id": execution_id,
            "status": status.status.value,
            "container_id": status.container_id,
            "current_step": status.current_step,
            "total_steps": status.total_steps,
            "steps": steps_details,
            "error_message": status.error_message,
            "fixes_applied": status.fixes_applied,
            "logs": status.logs
        }

        return jsonify(response)

    except Exception as e:
        logger.error(f"Error getting details for execution {execution_id}: {e}", exc_info=True)
        return jsonify({"error": "Internal server error"}), 500

@app.route('/cleanup', methods=['POST'])
def cleanup_completed_executions():
    """Clean up completed executions to free memory."""
    logger.info("Request to cleanup completed executions")

    try:
        cleaned_count = 0
        with execution_lock:
            completed_executions = []
            for exec_id, agent in active_executions.items():
                status = agent.get_status()
                if status.status in [ExecutionStatus.SUCCESS, ExecutionStatus.FAILED, ExecutionStatus.STOPPED]:
                    completed_executions.append(exec_id)

            for exec_id in completed_executions:
                del active_executions[exec_id]
                cleaned_count += 1

        response = {
            "message": f"Cleaned up {cleaned_count} completed executions",
            "cleaned_count": cleaned_count,
            "remaining_count": len(active_executions)
        }

        logger.info(f"Cleaned up {cleaned_count} completed executions")
        return jsonify(response)

    except Exception as e:
        logger.error(f"Error during cleanup: {e}", exc_info=True)
        return jsonify({"error": "Internal server error"}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for the service."""
    logger.debug("Health check endpoint was hit.")

    try:
        # Check Docker connectivity
        import docker
        docker_client = docker.from_env()
        docker_client.ping()
        docker_healthy = True
    except Exception as e:
        logger.warning(f"Docker health check failed: {e}")
        docker_healthy = False

    # Check AI availability
    ai_available = bool(os.getenv('GEMINI_API_KEY'))

    health_status = {
        "service": "ExecuteCmdService",
        "status": "healthy" if docker_healthy else "degraded",
        "docker_available": docker_healthy,
        "ai_available": ai_available,
        "active_executions": len(active_executions),
        "timestamp": time.time()
    }

    status_code = 200 if docker_healthy else 503
    return jsonify(health_status), status_code

# Background cleanup task
def background_cleanup():
    """Background task to periodically clean up old executions."""
    while True:
        try:
            time.sleep(300)  # Run every 5 minutes

            with execution_lock:
                old_executions = []

                for exec_id, agent in active_executions.items():
                    status = agent.get_status()
                    # Remove executions that have been completed
                    if status.status in [ExecutionStatus.SUCCESS, ExecutionStatus.FAILED, ExecutionStatus.STOPPED]:
                        old_executions.append(exec_id)

                # Keep only the last 10 completed executions
                if len(old_executions) > 10:
                    for exec_id in old_executions[:-10]:
                        del active_executions[exec_id]
                        logger.info(f"Auto-cleaned old execution: {exec_id}")

        except Exception as e:
            logger.error(f"Error in background cleanup: {e}")

# Start background cleanup thread
cleanup_thread = threading.Thread(target=background_cleanup, daemon=True)
cleanup_thread.start()

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8005))
    debug_mode = os.environ.get('FLASK_ENV') == 'development'

    if debug_mode:
        logger.setLevel(logging.DEBUG)
        logger.info("Service starting in DEBUG mode.")

    logger.info(f"ExecuteCmd service starting on http://0.0.0.0:{port}")
    logger.info("Docker-based AI execution engine ready")
    app.run(host='0.0.0.0', port=port, debug=debug_mode)
