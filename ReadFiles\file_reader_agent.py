# read-files/file_reader_agent.py
import logging
import os
import asyncio
import aiohttp
import random
from typing import List, Dict, Optional
from urllib.parse import urlparse

# --- Optional Imports for AI and Environment Variables ---
try:
    import google.generativeai as genai
    from google.api_core.exceptions import ResourceExhausted
    # <<< NEW: Import the library to parse detailed error messages from Google
    from google.rpc import error_details_pb2
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    genai = None
    ResourceExhausted = None
    error_details_pb2 = None

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

logger = logging.getLogger(__name__)

# <<< CHANGED: Greatly improved retry logic to be adaptive
async def retry_with_adaptive_backoff_async(func, max_retries=3):
    """
    Wraps an async function to automatically retry on Gemini rate limit errors,
    using the API's suggested backoff time.
    """
    async def wrapper(*args, **kwargs):
        for attempt in range(max_retries):
            try:
                return await func(*args, **kwargs)
            except ResourceExhausted as e:
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + random.uniform(0, 1) # Default backoff
                    
                    # <<< NEW: Try to parse the suggested retry delay from the error metadata
                    for detail in e.trailing_metadata():
                        if detail.key == 'google.rpc.retryinfo-bin':
                            try:
                                info = error_details_pb2.RetryInfo.FromString(detail.value)
                                suggested_wait = info.retry_delay.seconds + (info.retry_delay.nanos / 1e9)
                                # Use the suggested wait time if it's longer than our default
                                wait_time = max(wait_time, suggested_wait + random.uniform(0, 1))
                                logger.info(f"API suggested a retry delay of {suggested_wait:.2f}s.")
                            except Exception as parse_error:
                                logger.warning(f"Could not parse retry_delay from error metadata: {parse_error}")
                            break # Found the retry info, no need to look further
                    
                    logger.warning(
                        f"Rate limit hit (attempt {attempt + 1}/{max_retries}). "
                        f"Retrying in {wait_time:.2f} seconds."
                    )
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"AI call failed after {max_retries} retries.")
                    raise e
    return wrapper

class FileReaderAgent:
    """
    Reads files from a GitHub repository and analyzes their content concurrently using asyncio and Gemini AI.
    Includes a Semaphore and adaptive retries to handle API rate limits gracefully.
    """
    
    def __init__(self, repository_url: str, use_ai: bool = True):
        logger.info("Initializing FileReaderAgent...")
        self.repository_url = repository_url
        self.gemini_model = None
        self.gemini_semaphore = None
        
        self.owner, self.repo = self._parse_github_url(repository_url)
        
        logger.info(f"AI usage requested: {use_ai}")
        if use_ai and GEMINI_AVAILABLE:
            api_key = os.getenv('GEMINI_API_KEY')
            if api_key:
                try:
                    genai.configure(api_key=api_key)
                    self.gemini_model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')
                    logger.info("Gemini model initialized successfully.")
                    
                    concurrency_limit = 2 # Keep concurrency low to prevent triggering limits in the first place
                    self.gemini_semaphore = asyncio.Semaphore(concurrency_limit)
                    logger.info(f"Gemini API concurrency limit set to {concurrency_limit}.")

                except Exception as e:
                    logger.error(f"Failed to initialize Gemini: {e}")
            else:
                logger.warning("GEMINI_API_KEY not found. Using basic file reading.")
        elif use_ai and not GEMINI_AVAILABLE:
            logger.warning("AI requested but google-generativeai not installed. Using basic reading.")

        logger.info(f"FileReaderAgent initialized. AI analysis is {'ENABLED' if self.gemini_model else 'DISABLED'}.")

    def _parse_github_url(self, url: str) -> tuple:
        try:
            parsed = urlparse(url)
            path_parts = parsed.path.strip('/').split('/')
            if len(path_parts) >= 2:
                return path_parts[0], path_parts[1].replace('.git', '')
            else:
                raise ValueError("Invalid GitHub URL format")
        except Exception as e:
            logger.error(f"Failed to parse GitHub URL {url}: {e}")
            raise ValueError(f"Invalid GitHub URL: {url}")

    async def _fetch_file_content_async(self, session: aiohttp.ClientSession, file_path: str) -> Optional[str]:
        # This function is unchanged and correct.
        main_url = f"https://raw.githubusercontent.com/{self.owner}/{self.repo}/main/{file_path}"
        try:
            async with session.get(main_url, timeout=30) as response:
                if response.status == 200: return await response.text()
                if response.status == 404:
                    master_url = f"https://raw.githubusercontent.com/{self.owner}/{self.repo}/master/{file_path}"
                    async with session.get(master_url, timeout=30) as master_response:
                        if master_response.status == 200: return await master_response.text()
                        return None
        except (asyncio.TimeoutError, aiohttp.ClientError):
            return None

    async def _process_one_file_async(self, session: aiohttp.ClientSession, file_info: Dict) -> Dict:
        file_path = file_info.get('path')
        reason = file_info.get('reason', 'No reason provided')
        
        content = await self._fetch_file_content_async(session, file_path)
        file_result = {"path": file_path, "reason": reason, "content": content, "analysis": None, "error": None}
        
        if content is None:
            file_result["error"] = "Failed to fetch file content"
        elif self.gemini_model:
            try:
                async with self.gemini_semaphore:
                    # <<< CHANGED: Use the new adaptive retry logic
                    retryable_analysis = await retry_with_adaptive_backoff_async(self._analyze_file_with_ai_async)
                    file_result["analysis"] = await retryable_analysis(file_path, content, reason)
            except Exception as e:
                error_msg = f"AI analysis failed permanently for {file_path} after multiple retries."
                logger.error(error_msg, exc_info=False)
                file_result["error"] = "AI analysis failed due to persistent rate limiting or another error."
        
        return file_result

    async def read_and_analyze_files_async(self, files_to_read: List[Dict]) -> Dict:
        logger.info(f"Starting to read/analyze {len(files_to_read)} files with adaptive retries...")
        
        results = {"repository_url": self.repository_url, "files_analyzed": [], "summary": None, "errors": []}
        
        async with aiohttp.ClientSession() as session:
            tasks = [self._process_one_file_async(session, file_info) for file_info in files_to_read]
            processed_files = await asyncio.gather(*tasks, return_exceptions=True)

        for file_result in processed_files:
            if isinstance(file_result, Exception):
                logger.error(f"A critical error occurred in a processing task: {file_result}")
                results["errors"].append(f"A task failed with an unhandled exception: {file_result}")
                continue
            if file_result.get("error"):
                results["errors"].append(f"Could not process {file_result['path']}: {file_result['error']}")
            results["files_analyzed"].append(file_result)

        if self.gemini_model and any(f.get('content') and not f.get('error') for f in results["files_analyzed"]):
            try:
                async with self.gemini_semaphore:
                    # <<< CHANGED: Use the new adaptive retry logic for the summary
                    retryable_summary = await retry_with_adaptive_backoff_async(self._generate_overall_summary_async)
                    results["summary"] = await retryable_summary(results["files_analyzed"])
            except Exception as e:
                logger.error(f"Failed to generate overall summary after retries: {e}", exc_info=False)
                results["errors"].append("Summary generation failed due to persistent rate limiting or another error.")
        
        logger.info(f"Completed analysis of {len(files_to_read)} files with {len(results['errors'])} errors.")
        return results

    async def _analyze_file_with_ai_async(self, file_path: str, content: str, reason: str) -> str:
        # This core function is unchanged. It's wrapped by the retry logic.
        logger.debug(f"Attempting AI analysis for {file_path}")
        prompt = f"""You are a DevOps engineer analyzing a critical file from a repository. 
File: {file_path}
Reason for selection: {reason}
File Content:
```
{content}
```

Please provide a concise analysis focusing on:
1. What this file does and its purpose
2. Key configuration details, dependencies, or setup requirements
3. Any important environment variables, ports, or external dependencies
4. Build/deployment instructions or requirements mentioned
5. Any potential issues or missing configurations
Keep your analysis practical and focused on what a developer needs to know to run this application."""
        
        response = await self.gemini_model.generate_content_async(prompt)
        return response.text

    async def _generate_overall_summary_async(self, files_analyzed: List[Dict]) -> str:
        # This core function is unchanged. It's wrapped by the retry logic.
        logger.debug("Attempting to generate overall summary.")
        file_summaries = []
        for file_info in files_analyzed:
            if file_info.get('content') and not file_info.get('error'):
                summary = f"File: {file_info['path']}\nPurpose: {file_info['reason']}\n"
                if file_info.get('analysis'):
                    summary += f"Analysis: {file_info['analysis']}\n"
                summary += "---\n"
                file_summaries.append(summary)
        
        if not file_summaries:
            return "No files were successfully analyzed to generate a summary."
        
        prompt = f"""Based on the analysis of these critical files from a repository, provide a comprehensive summary that includes:
1. **Application Type & Technology Stack**: What kind of application this is and what technologies it uses
2. **Architecture Overview**: How the application is structured (microservices, monolith, etc.)
3. **Setup Requirements**: What needs to be installed or configured to run this application
4. **Build & Run Instructions**: How to build and start the application
5. **Key Dependencies**: Important external services, databases, or APIs required
6. **Deployment Considerations**: Any containerization, environment variables, or deployment notes
File Analysis Results:
{chr(10).join(file_summaries)}
Provide a clear, actionable summary that would help a new developer understand and run this application."""
        
        response = await self.gemini_model.generate_content_async(prompt)
        return response.text