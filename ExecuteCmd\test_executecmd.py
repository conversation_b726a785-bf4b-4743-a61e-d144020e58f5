#!/usr/bin/env python3
"""
Test script for ExecuteCmd microservice.
This script demonstrates how to use the ExecuteCmd service to execute setup chains.
"""

import requests
import json
import time
import sys
from typing import Dict, Any

class ExecuteCmdTester:
    def __init__(self, base_url: str = "http://localhost:8005"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_health(self) -> bool:
        """Test the health endpoint."""
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ Service is healthy: {health_data}")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False
    
    def execute_sample_setup_chain(self) -> str:
        """Execute a sample setup chain and return execution ID."""
        # Sample setup chain for a simple Python application
        setup_chain = [
            {
                "step_number": 1,
                "title": "Clone Repository",
                "description": "Clone the repository to the container",
                "commands": [
                    "git clone https://github.com/AliSoua/HR-Management.git",
                    "cd HR-Management"
                ],
                "working_directory": "/workspace",
                "prerequisites": ["Git installed"],
                "notes": ["Repository will be cloned to /workspace"],
                "estimated_time": "1-2 minutes"
            },
            {
                "step_number": 2,
                "title": "Create Python Virtual Environment",
                "description": "Set up Python virtual environment",
                "commands": [
                    "python3 -m venv venv",
                    "source venv/bin/activate"
                ],
                "working_directory": "/workspace/HR-Management",
                "prerequisites": ["Python 3 installed"],
                "notes": ["Virtual environment isolates dependencies"],
                "estimated_time": "1-2 minutes"
            },
            {
                "step_number": 3,
                "title": "Install Dependencies",
                "description": "Install Python dependencies",
                "commands": [
                    "pip install flask google-generativeai python-dotenv mysql-connector-python"
                ],
                "working_directory": "/workspace/HR-Management",
                "prerequisites": ["Virtual environment activated"],
                "notes": ["Installing required packages"],
                "estimated_time": "2-3 minutes"
            },
            {
                "step_number": 4,
                "title": "Verify Installation",
                "description": "Verify that the application can start",
                "commands": [
                    "python3 -c \"import flask, google.generativeai; print('Dependencies installed successfully')\""
                ],
                "working_directory": "/workspace/HR-Management",
                "prerequisites": ["Dependencies installed"],
                "notes": ["Quick verification of installation"],
                "estimated_time": "30 seconds"
            }
        ]
        
        payload = {
            "repository_url": "https://github.com/AliSoua/HR-Management",
            "setup_chain": setup_chain,
            "execution_options": {
                "base_image": "ubuntu:22.04",
                "timeout": 1800,
                "auto_fix": True,
                "max_retries_per_step": 3
            }
        }
        
        try:
            print("🚀 Starting execution...")
            response = self.session.post(
                f"{self.base_url}/execute-setup-chain",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                execution_id = result["execution_id"]
                print(f"✅ Execution started: {execution_id}")
                print(f"📊 Status: {result['status']}")
                print(f"🐳 Container: {result.get('container_id', 'N/A')}")
                return execution_id
            else:
                print(f"❌ Failed to start execution: {response.status_code}")
                print(f"Error: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error starting execution: {e}")
            return None
    
    def monitor_execution(self, execution_id: str) -> bool:
        """Monitor an execution until completion."""
        print(f"\n📊 Monitoring execution {execution_id}...")
        
        while True:
            try:
                # Get status
                response = self.session.get(f"{self.base_url}/execution/{execution_id}/status")
                
                if response.status_code == 200:
                    status_data = response.json()
                    status = status_data["status"]
                    progress = status_data["progress"]
                    
                    print(f"Status: {status} | Step: {progress['current_step']}/{progress['total_steps']} ({progress['percentage']}%)")
                    
                    # Show step details
                    if "steps" in status_data:
                        for step in status_data["steps"]:
                            step_status = step["status"]
                            step_icon = "✅" if step_status == "success" else "❌" if step_status == "failed" else "🔄" if step_status == "running" else "⏳"
                            print(f"  {step_icon} Step {step['step_number']}: {step['title']} ({step_status})")
                            
                            if step.get("has_error") and step.get("error_preview"):
                                print(f"    ⚠️ Error: {step['error_preview']}")
                    
                    # Show fixes applied
                    if status_data.get("fixes_applied"):
                        print(f"🔧 Fixes applied: {len(status_data['fixes_applied'])}")
                        for fix in status_data["fixes_applied"]:
                            print(f"  - {fix}")
                    
                    # Check if completed
                    if status in ["success", "failed", "stopped"]:
                        print(f"\n🏁 Execution completed with status: {status}")
                        if status == "success":
                            print("✅ Setup chain executed successfully!")
                            return True
                        else:
                            print(f"❌ Execution failed: {status_data.get('error_message', 'Unknown error')}")
                            return False
                    
                    time.sleep(5)  # Wait 5 seconds before next check
                    
                else:
                    print(f"❌ Error getting status: {response.status_code}")
                    return False
                    
            except KeyboardInterrupt:
                print("\n⏹️ Monitoring interrupted by user")
                return False
            except Exception as e:
                print(f"❌ Error monitoring execution: {e}")
                time.sleep(5)
    
    def get_execution_logs(self, execution_id: str) -> None:
        """Get and display execution logs."""
        try:
            response = self.session.get(f"{self.base_url}/execution/{execution_id}/logs")
            
            if response.status_code == 200:
                logs_data = response.json()
                logs = logs_data["logs"]
                
                print(f"\n📋 Execution Logs ({len(logs)} entries):")
                print("=" * 60)
                for log in logs:
                    print(log)
                print("=" * 60)
                
            else:
                print(f"❌ Error getting logs: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error getting logs: {e}")
    
    def list_executions(self) -> None:
        """List all executions."""
        try:
            response = self.session.get(f"{self.base_url}/executions")
            
            if response.status_code == 200:
                data = response.json()
                executions = data["executions"]
                
                print(f"\n📋 Active Executions ({len(executions)}):")
                for execution in executions:
                    print(f"  - {execution['execution_id']}: {execution['status']} "
                          f"(Step {execution['current_step']}/{execution['total_steps']})")
                
            else:
                print(f"❌ Error listing executions: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error listing executions: {e}")

def main():
    """Main test function."""
    print("🧪 ExecuteCmd Microservice Tester")
    print("=" * 40)
    
    tester = ExecuteCmdTester()
    
    # Test health
    if not tester.test_health():
        print("❌ Service is not healthy. Please check if the service is running.")
        sys.exit(1)
    
    # List current executions
    tester.list_executions()
    
    # Execute sample setup chain
    execution_id = tester.execute_sample_setup_chain()
    
    if execution_id:
        # Monitor execution
        success = tester.monitor_execution(execution_id)
        
        # Get logs
        tester.get_execution_logs(execution_id)
        
        if success:
            print("\n🎉 Test completed successfully!")
        else:
            print("\n💥 Test failed!")
    else:
        print("\n💥 Failed to start execution!")

if __name__ == "__main__":
    main()
