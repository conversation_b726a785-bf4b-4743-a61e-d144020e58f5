#!/usr/bin/env python3
"""
Test WebSocket server fix.
Simple test to verify WebSocket connections work properly.
"""

import asyncio
import websockets
import json
import time
import sys

async def test_websocket_connection():
    """Test basic WebSocket connection."""
    print("🧪 Testing WebSocket Connection")
    print("=" * 40)
    
    try:
        # Connect to WebSocket server
        print("📡 Connecting to WebSocket server...")
        websocket = await websockets.connect("ws://localhost:8006")
        print("✅ Connected successfully!")
        
        # Send ping message
        print("📤 Sending ping message...")
        ping_message = {
            "type": "ping",
            "timestamp": time.time()
        }
        await websocket.send(json.dumps(ping_message))
        
        # Wait for response
        print("👂 Waiting for response...")
        response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
        
        try:
            data = json.loads(response)
            if data.get("type") == "pong":
                print("✅ Received pong response!")
                print(f"   Response: {data}")
            else:
                print(f"📨 Received: {data}")
        except json.JSONDecodeError:
            print(f"📨 Received (raw): {response}")
        
        # Test subscription
        print("📤 Testing execution subscription...")
        subscribe_message = {
            "type": "subscribe",
            "execution_id": "test_execution_123"
        }
        await websocket.send(json.dumps(subscribe_message))
        
        # Wait for subscription response
        response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
        try:
            data = json.loads(response)
            if data.get("type") == "subscribed":
                print("✅ Subscription successful!")
                print(f"   Response: {data}")
            else:
                print(f"📨 Received: {data}")
        except json.JSONDecodeError:
            print(f"📨 Received (raw): {response}")
        
        # Close connection
        print("🔌 Closing connection...")
        await websocket.close()
        print("✅ Connection closed successfully!")
        
        return True
        
    except asyncio.TimeoutError:
        print("❌ Timeout waiting for WebSocket response")
        return False
    except ConnectionRefusedError:
        print("❌ Connection refused - is the WebSocket server running?")
        print("   Start it with: python realtime_app.py")
        return False
    except Exception as e:
        print(f"❌ WebSocket test error: {e}")
        return False

async def test_multiple_connections():
    """Test multiple simultaneous connections."""
    print("\n🧪 Testing Multiple Connections")
    print("=" * 40)
    
    try:
        # Create multiple connections
        connections = []
        for i in range(3):
            print(f"📡 Creating connection {i+1}...")
            websocket = await websockets.connect("ws://localhost:8006")
            connections.append(websocket)
        
        print(f"✅ Created {len(connections)} connections successfully!")
        
        # Send messages from all connections
        for i, websocket in enumerate(connections):
            message = {
                "type": "ping",
                "client_id": f"client_{i+1}",
                "timestamp": time.time()
            }
            await websocket.send(json.dumps(message))
            print(f"📤 Sent message from client {i+1}")
        
        # Receive responses
        for i, websocket in enumerate(connections):
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                data = json.loads(response)
                print(f"📨 Client {i+1} received: {data.get('type', 'unknown')}")
            except asyncio.TimeoutError:
                print(f"⏰ Client {i+1} timed out")
        
        # Close all connections
        for i, websocket in enumerate(connections):
            await websocket.close()
            print(f"🔌 Closed connection {i+1}")
        
        print("✅ Multiple connections test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Multiple connections test error: {e}")
        return False

def check_server_running():
    """Check if the real-time server is running."""
    import requests
    
    try:
        response = requests.get("http://localhost:8007/health", timeout=3)
        if response.status_code == 200:
            health = response.json()
            print("✅ Real-time server is running")
            print(f"   WebSocket running: {health.get('websocket_running', False)}")
            print(f"   WebSocket clients: {health.get('websocket_clients', 0)}")
            return True
        else:
            print(f"⚠️ Server responded with status: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Real-time server is not running")
        print("   Start it with: python realtime_app.py")
        return False
    except Exception as e:
        print(f"❌ Error checking server: {e}")
        return False

async def main():
    """Main test function."""
    print("🚀 WebSocket Server Fix Test")
    print("Testing WebSocket connections after fixing handler signature")
    print("=" * 60)
    
    # Check if server is running
    print("1. Checking if real-time server is running...")
    server_running = check_server_running()
    
    if not server_running:
        print("\n💡 To start the server, run:")
        print("   python realtime_app.py")
        return False
    
    # Test basic connection
    print("\n2. Testing basic WebSocket connection...")
    basic_test = await test_websocket_connection()
    
    if not basic_test:
        print("❌ Basic WebSocket test failed!")
        return False
    
    # Test multiple connections
    print("\n3. Testing multiple connections...")
    multi_test = await test_multiple_connections()
    
    if not multi_test:
        print("❌ Multiple connections test failed!")
        return False
    
    print("\n🎉 All WebSocket tests passed!")
    print("The WebSocket server is working correctly.")
    print("\n💡 Next steps:")
    print("   1. Open realtime_demo.html in your browser")
    print("   2. Click 'Connect to WebSocket'")
    print("   3. Start a real-time execution")
    
    return True

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(1)
