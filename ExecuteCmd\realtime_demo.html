<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-time ExecuteCmd Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .content {
            padding: 20px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input, textarea, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.executing {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        
        .log-container {
            height: 400px;
            overflow-y: auto;
            background: #1e1e1e;
            color: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.success { color: #4caf50; }
        .log-entry.error { color: #f44336; }
        .log-entry.warning { color: #ff9800; }
        .log-entry.info { color: #2196f3; }
        .log-entry.step { color: #9c27b0; font-weight: bold; }
        
        .step-list {
            list-style: none;
            padding: 0;
        }
        
        .step-item {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
            border-left: 4px solid #ddd;
            background: white;
        }
        
        .step-item.pending { border-left-color: #ffc107; }
        .step-item.running { border-left-color: #2196f3; }
        .step-item.success { border-left-color: #4caf50; }
        .step-item.failed { border-left-color: #f44336; }
        .step-item.analyzing { border-left-color: #9c27b0; }
        .step-item.fixing { border-left-color: #ff5722; }
        
        .step-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .step-details {
            font-size: 12px;
            color: #666;
        }
        
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Real-time ExecuteCmd Demo</h1>
            <p>Step-by-step execution with WebSocket updates and AI error recovery</p>
        </div>
        
        <div class="content">
            <!-- Connection Status -->
            <div class="section">
                <h3>📡 Connection Status</h3>
                <div id="connectionStatus" class="status disconnected">
                    Disconnected from WebSocket
                </div>
                <button id="connectBtn" onclick="connectWebSocket()">Connect to WebSocket</button>
            </div>
            
            <!-- Execution Control -->
            <div class="section">
                <h3>🎯 Start Execution</h3>
                <div class="input-group">
                    <label for="repositoryUrl">Repository URL:</label>
                    <input type="text" id="repositoryUrl" value="https://github.com/AliSoua/HR-Management" placeholder="Enter GitHub repository URL">
                </div>
                <button id="startBtn" onclick="startExecution()" disabled>Start Real-time Execution</button>
            </div>
            
            <!-- Progress and Steps -->
            <div class="two-column">
                <div class="section">
                    <h3>📊 Execution Progress</h3>
                    <div id="progressInfo">No execution running</div>
                    <div class="progress-bar">
                        <div id="progressFill" class="progress-fill" style="width: 0%"></div>
                    </div>
                    <div id="executionId" style="font-size: 12px; color: #666;"></div>
                </div>
                
                <div class="section">
                    <h3>📝 Step Status</h3>
                    <ul id="stepList" class="step-list">
                        <li>No steps to display</li>
                    </ul>
                </div>
            </div>
            
            <!-- Real-time Logs -->
            <div class="section">
                <h3>📋 Real-time Logs</h3>
                <div id="logContainer" class="log-container">
                    <div class="log-entry info">Ready to start execution...</div>
                </div>
                <button onclick="clearLogs()" style="margin-top: 10px; width: auto; padding: 5px 15px;">Clear Logs</button>
            </div>
        </div>
    </div>

    <script>
        let websocket = null;
        let currentExecutionId = null;
        let steps = {};
        
        // WebSocket connection
        function connectWebSocket() {
            const statusEl = document.getElementById('connectionStatus');
            const connectBtn = document.getElementById('connectBtn');
            const startBtn = document.getElementById('startBtn');
            
            try {
                websocket = new WebSocket('ws://localhost:8006');
                
                websocket.onopen = function(event) {
                    statusEl.textContent = 'Connected to WebSocket';
                    statusEl.className = 'status connected';
                    connectBtn.textContent = 'Disconnect';
                    connectBtn.onclick = disconnectWebSocket;
                    startBtn.disabled = false;
                    addLog('Connected to WebSocket server', 'success');
                };
                
                websocket.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        handleWebSocketMessage(data);
                    } catch (e) {
                        addLog('Error parsing WebSocket message: ' + e.message, 'error');
                    }
                };
                
                websocket.onclose = function(event) {
                    statusEl.textContent = 'Disconnected from WebSocket';
                    statusEl.className = 'status disconnected';
                    connectBtn.textContent = 'Connect to WebSocket';
                    connectBtn.onclick = connectWebSocket;
                    startBtn.disabled = true;
                    addLog('WebSocket connection closed', 'warning');
                };
                
                websocket.onerror = function(error) {
                    addLog('WebSocket error: ' + error.message, 'error');
                };
                
            } catch (e) {
                addLog('Failed to connect to WebSocket: ' + e.message, 'error');
            }
        }
        
        function disconnectWebSocket() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
        }
        
        // Start execution
        async function startExecution() {
            const repositoryUrl = document.getElementById('repositoryUrl').value;
            if (!repositoryUrl) {
                addLog('Please enter a repository URL', 'error');
                return;
            }
            
            const setupChain = [
                {
                    "commands": ["python3 --version", "git --version"],
                    "description": "Check basic tools availability",
                    "step_number": 1,
                    "title": "Prerequisites Check",
                    "working_directory": "~/"
                },
                {
                    "commands": ["git clone " + repositoryUrl, "ls -la"],
                    "description": "Clone repository and verify",
                    "step_number": 2,
                    "title": "Clone Repository",
                    "working_directory": "~/"
                },
                {
                    "commands": ["python3 -m venv venv", "echo 'Virtual environment created'"],
                    "description": "Create Python virtual environment",
                    "step_number": 3,
                    "title": "Create Virtual Environment",
                    "working_directory": "./HR-Management"
                }
            ];
            
            try {
                addLog('Starting execution for: ' + repositoryUrl, 'info');
                
                const response = await fetch('http://localhost:8007/execute-setup-chain-realtime', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        repository_url: repositoryUrl,
                        setup_chain: setupChain
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    currentExecutionId = result.execution_id;
                    
                    document.getElementById('executionId').textContent = 'Execution ID: ' + currentExecutionId;
                    addLog('Execution started: ' + currentExecutionId, 'success');
                    
                    // Subscribe to execution updates
                    if (websocket && websocket.readyState === WebSocket.OPEN) {
                        websocket.send(JSON.stringify({
                            type: 'subscribe',
                            execution_id: currentExecutionId
                        }));
                    }
                    
                    // Initialize steps
                    initializeSteps(setupChain);
                    
                } else {
                    const error = await response.text();
                    addLog('Failed to start execution: ' + error, 'error');
                }
                
            } catch (e) {
                addLog('Error starting execution: ' + e.message, 'error');
            }
        }
        
        // Handle WebSocket messages
        function handleWebSocketMessage(data) {
            const type = data.type;
            
            if (type === 'connection') {
                addLog(data.message, 'info');
            } else if (type === 'subscribed') {
                addLog('Subscribed to execution: ' + data.execution_id, 'info');
            } else if (type === 'execution_update') {
                handleExecutionUpdate(data);
            } else if (type === 'error') {
                addLog('WebSocket error: ' + data.message, 'error');
            }
        }
        
        // Handle execution updates
        function handleExecutionUpdate(data) {
            const currentStep = data.current_step || 0;
            const totalSteps = data.total_steps || 0;
            const progress = data.progress_percentage || 0;
            const message = data.message || '';
            const status = data.status || 'unknown';
            
            // Update progress
            document.getElementById('progressInfo').textContent = 
                `Step ${currentStep}/${totalSteps} (${progress.toFixed(1)}%) - ${status}`;
            document.getElementById('progressFill').style.width = progress + '%';
            
            // Add log entry
            if (message) {
                addLog(`[${currentStep}/${totalSteps}] ${message}`, 'step');
            }
            
            // Handle step result
            if (data.step_result) {
                updateStepStatus(data.step_result);
            }
            
            // Check if completed
            if (status === 'success' || status === 'failed') {
                addLog(`Execution completed with status: ${status}`, status === 'success' ? 'success' : 'error');
            }
        }
        
        // Initialize steps display
        function initializeSteps(setupChain) {
            const stepList = document.getElementById('stepList');
            stepList.innerHTML = '';
            steps = {};
            
            setupChain.forEach(step => {
                const stepEl = document.createElement('li');
                stepEl.className = 'step-item pending';
                stepEl.id = 'step-' + step.step_number;
                
                stepEl.innerHTML = `
                    <div class="step-title">⏳ Step ${step.step_number}: ${step.title}</div>
                    <div class="step-details">${step.description}</div>
                `;
                
                stepList.appendChild(stepEl);
                steps[step.step_number] = { element: stepEl, status: 'pending' };
            });
        }
        
        // Update step status
        function updateStepStatus(stepResult) {
            const stepNumber = stepResult.step_number;
            const stepData = steps[stepNumber];
            
            if (!stepData) return;
            
            const status = stepResult.status;
            const title = stepResult.title;
            const command = stepResult.command || '';
            const duration = stepResult.duration || 0;
            const fixApplied = stepResult.fix_applied || '';
            
            // Status icons
            const icons = {
                pending: '⏳',
                running: '🔄',
                success: '✅',
                failed: '❌',
                analyzing: '🤖',
                fixing: '🔧'
            };
            
            const icon = icons[status] || '❓';
            
            // Update step element
            stepData.element.className = `step-item ${status}`;
            
            let details = `Status: ${status}`;
            if (command) details += `<br>Command: ${command}`;
            if (duration > 0) details += `<br>Duration: ${duration.toFixed(1)}s`;
            if (fixApplied) details += `<br>AI Fix: ${fixApplied}`;
            
            stepData.element.innerHTML = `
                <div class="step-title">${icon} Step ${stepNumber}: ${title}</div>
                <div class="step-details">${details}</div>
            `;
            
            stepData.status = status;
            
            // Add detailed log
            addLog(`Step ${stepNumber} (${title}): ${status}`, status === 'success' ? 'success' : status === 'failed' ? 'error' : 'info');
            if (command) addLog(`  Command: ${command}`, 'info');
            if (fixApplied) addLog(`  AI Fix Applied: ${fixApplied}`, 'warning');
        }
        
        // Add log entry
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // Clear logs
        function clearLogs() {
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = '<div class="log-entry info">Logs cleared...</div>';
        }
        
        // Auto-connect on page load
        window.onload = function() {
            addLog('Page loaded. Click "Connect to WebSocket" to start.', 'info');
        };
    </script>
</body>
</html>
