#!/usr/bin/env python3
"""
Test ExecuteCmd with <PERSON><PERSON> fallback to simulation mode.
This test will try <PERSON><PERSON> first, then automatically fall back to simulation if <PERSON><PERSON> fails.
"""

import requests
import json
import time
import sys

def test_docker_with_fallback():
    """Test ExecuteCmd with automatic Docker fallback."""
    base_url = "http://localhost:8005"
    
    print("🐳➡️🎭 Docker Fallback Test")
    print("This test tries <PERSON><PERSON> first, then falls back to simulation if needed")
    print("=" * 70)
    
    # Simple setup chain for testing
    payload = {
        "repository_url": "https://github.com/AliSoua/HR-Management",
        "setup_chain": [
            {
                "commands": [
                    "python3 --version",
                    "git --version"
                ],
                "description": "Check basic tools availability",
                "estimated_time": "30 seconds",
                "notes": ["Basic version checks"],
                "prerequisites": [],
                "step_number": 1,
                "title": "Prerequisites Check",
                "working_directory": "~/"
            },
            {
                "commands": [
                    "git clone https://github.com/AliSoua/HR-Management.git",
                    "cd HR-Management",
                    "ls -la"
                ],
                "description": "Clone repository and list contents",
                "estimated_time": "1-2 minutes",
                "notes": [],
                "prerequisites": ["Git installed"],
                "step_number": 2,
                "title": "Clone and Explore Repository",
                "working_directory": "~/"
            },
            {
                "commands": [
                    "python3 -m venv venv",
                    "echo 'Virtual environment created'"
                ],
                "description": "Create Python virtual environment",
                "estimated_time": "1 minute",
                "notes": [],
                "prerequisites": ["Python installed"],
                "step_number": 3,
                "title": "Create Virtual Environment",
                "working_directory": "./HR-Management"
            }
        ],
        "execution_options": {
            "base_image": "ubuntu:22.04",
            "timeout": 1800,
            "auto_fix": True,
            "max_retries_per_step": 3
            # Note: NOT forcing simulation mode - let it try Docker first
        }
    }
    
    print(f"📋 Test Details:")
    print(f"   Repository: {payload['repository_url']}")
    print(f"   Steps: {len(payload['setup_chain'])}")
    print(f"   Mode: Docker first, simulation fallback")
    
    # Test health first
    print(f"\n1. Testing service health...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Service is healthy")
            print(f"   Docker available: {health_data.get('docker_available', False)}")
            print(f"   AI available: {health_data.get('ai_available', False)}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # Start execution
    print(f"\n2. Starting execution (Docker first, simulation fallback)...")
    try:
        response = requests.post(
            f"{base_url}/execute-setup-chain",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            execution_id = result["execution_id"]
            print(f"✅ Execution started: {execution_id}")
            print(f"📊 Status: {result['status']}")
            print(f"🐳 Container: {result.get('container_id', 'N/A')}")
            
            # Monitor execution
            return monitor_execution(base_url, execution_id)
        else:
            print(f"❌ Failed to start execution: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error starting execution: {e}")
        return False

def monitor_execution(base_url: str, execution_id: str) -> bool:
    """Monitor execution progress and detect mode switches."""
    print(f"\n3. Monitoring execution {execution_id}...")
    print("=" * 50)
    
    start_time = time.time()
    last_step = 0
    mode_switched = False
    
    while True:
        try:
            response = requests.get(f"{base_url}/execution/{execution_id}/status")
            
            if response.status_code == 200:
                status_data = response.json()
                status = status_data["status"]
                progress = status_data["progress"]
                current_step = progress['current_step']
                
                elapsed = int(time.time() - start_time)
                print(f"[{elapsed:03d}s] Status: {status} | Step: {current_step}/{progress['total_steps']} ({progress['percentage']}%)")
                
                # Check for mode switch to simulation
                if status_data.get("fixes_applied"):
                    for fix in status_data["fixes_applied"]:
                        if "simulation mode" in fix.lower() and not mode_switched:
                            print(f"  🎭 MODE SWITCH: {fix}")
                            mode_switched = True
                
                # Show new step details
                if current_step > last_step and "steps" in status_data:
                    for step in status_data["steps"]:
                        if step["step_number"] == current_step:
                            mode_icon = "🎭" if mode_switched else "🐳"
                            print(f"  {mode_icon} Executing: {step['title']}")
                            if step.get("retry_count", 0) > 0:
                                print(f"     🔄 Retried {step['retry_count']} times")
                            break
                    last_step = current_step
                
                # Show step status
                if "steps" in status_data:
                    for step in status_data["steps"]:
                        step_status = step["status"]
                        if step_status == "success":
                            step_icon = "✅"
                        elif step_status == "failed":
                            step_icon = "❌"
                        elif step_status == "running":
                            step_icon = "🎭" if mode_switched else "🐳"
                        elif step_status == "error_recovery":
                            step_icon = "🔧"
                        else:
                            step_icon = "⏳"
                            
                        if step["step_number"] <= current_step:
                            print(f"     {step_icon} Step {step['step_number']}: {step['title']} ({step_status})")
                
                # Check if completed
                if status in ["success", "failed", "stopped"]:
                    elapsed = int(time.time() - start_time)
                    print(f"\n🏁 Execution completed in {elapsed}s with status: {status}")
                    
                    if status == "success":
                        if mode_switched:
                            print("✅ Execution succeeded after switching to simulation mode!")
                        else:
                            print("✅ Execution succeeded in Docker mode!")
                        show_execution_summary(base_url, execution_id, mode_switched)
                        return True
                    else:
                        print(f"❌ Execution failed: {status_data.get('error_message', 'Unknown error')}")
                        show_execution_summary(base_url, execution_id, mode_switched)
                        return False
                
                time.sleep(3)  # Check every 3 seconds
                
            else:
                print(f"❌ Error getting status: {response.status_code}")
                return False
                
        except KeyboardInterrupt:
            print("\n⏹️ Monitoring interrupted by user")
            return False
        except Exception as e:
            print(f"❌ Error monitoring execution: {e}")
            time.sleep(3)

def show_execution_summary(base_url: str, execution_id: str, mode_switched: bool):
    """Show execution summary with mode information."""
    try:
        response = requests.get(f"{base_url}/execution/{execution_id}/logs")
        
        if response.status_code == 200:
            logs_data = response.json()
            logs = logs_data["logs"]
            
            print(f"\n📋 Execution Summary:")
            print(f"   Execution ID: {execution_id}")
            print(f"   Mode Switch: {'Yes (Docker → Simulation)' if mode_switched else 'No (Docker only)'}")
            print(f"   Total Logs: {len(logs)}")
            
            print(f"\n📋 Key Logs:")
            print("-" * 40)
            for log in logs:
                if any(keyword in log.lower() for keyword in ['container', 'simulation', 'switch', 'fallback', 'mode']):
                    print(f"   {log}")
            print("-" * 40)
            
    except Exception as e:
        print(f"❌ Error getting execution summary: {e}")

def main():
    """Main test function."""
    print("🧪 ExecuteCmd Docker Fallback Test")
    print("Tests automatic fallback from Docker to simulation mode")
    print("=" * 60)
    
    success = test_docker_with_fallback()
    
    if success:
        print("\n🎉 Docker fallback test completed successfully!")
        print("The service can handle Docker issues gracefully.")
    else:
        print("\n💥 Docker fallback test failed!")
        print("Check the service logs for details.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
