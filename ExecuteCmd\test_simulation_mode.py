#!/usr/bin/env python3
"""
Test ExecuteCmd service in simulation mode (without <PERSON><PERSON>).
This allows testing the service functionality even when Docker is not available.
"""

import requests
import json
import time
import sys

def test_executecmd_simulation():
    """Test ExecuteCmd service in simulation mode."""
    base_url = "http://localhost:8005"
    
    print("🧪 Testing ExecuteCmd in Simulation Mode")
    print("=" * 50)
    
    # Test health endpoint
    print("\n1. Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Service is healthy")
            print(f"   Docker available: {health_data.get('docker_available', False)}")
            print(f"   AI available: {health_data.get('ai_available', False)}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # Sample setup chain for testing
    setup_chain = [
        {
            "step_number": 1,
            "title": "Clone Repository",
            "description": "Clone the repository",
            "commands": [
                "git clone https://github.com/AliSoua/HR-Management.git",
                "cd HR-Management"
            ],
            "working_directory": "/workspace",
            "prerequisites": ["Git installed"],
            "notes": ["Repository will be cloned"],
            "estimated_time": "1-2 minutes"
        },
        {
            "step_number": 2,
            "title": "Create Virtual Environment",
            "description": "Set up Python virtual environment",
            "commands": [
                "python -m venv venv",
                "source venv/bin/activate"
            ],
            "working_directory": "/workspace/HR-Management",
            "prerequisites": ["Python 3 installed"],
            "notes": ["Virtual environment isolates dependencies"],
            "estimated_time": "1-2 minutes"
        },
        {
            "step_number": 3,
            "title": "Install Dependencies",
            "description": "Install Python dependencies",
            "commands": [
                "pip install flask google-generativeai python-dotenv"
            ],
            "working_directory": "/workspace/HR-Management",
            "prerequisites": ["Virtual environment activated"],
            "notes": ["Installing required packages"],
            "estimated_time": "2-3 minutes"
        }
    ]
    
    # Test execution with simulation mode
    print("\n2. Testing execution with simulation mode...")
    payload = {
        "repository_url": "https://github.com/AliSoua/HR-Management",
        "setup_chain": setup_chain,
        "execution_options": {
            "base_image": "ubuntu:22.04",
            "timeout": 1800,
            "auto_fix": True,
            "max_retries_per_step": 3,
            "simulation_mode": True  # Force simulation mode
        }
    }
    
    try:
        response = requests.post(
            f"{base_url}/execute-setup-chain",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            execution_id = result["execution_id"]
            print(f"✅ Execution started: {execution_id}")
            print(f"📊 Status: {result['status']}")
            print(f"🎭 Container: {result.get('container_id', 'N/A')}")
            
            # Monitor execution
            return monitor_execution(base_url, execution_id)
        else:
            print(f"❌ Failed to start execution: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error starting execution: {e}")
        return False

def monitor_execution(base_url: str, execution_id: str) -> bool:
    """Monitor execution progress."""
    print(f"\n3. Monitoring execution {execution_id}...")
    
    start_time = time.time()
    
    while True:
        try:
            response = requests.get(f"{base_url}/execution/{execution_id}/status")
            
            if response.status_code == 200:
                status_data = response.json()
                status = status_data["status"]
                progress = status_data["progress"]
                
                elapsed = int(time.time() - start_time)
                print(f"[{elapsed:03d}s] Status: {status} | Step: {progress['current_step']}/{progress['total_steps']} ({progress['percentage']}%)")
                
                # Show step details
                if "steps" in status_data:
                    for step in status_data["steps"]:
                        step_status = step["status"]
                        step_icon = "✅" if step_status == "success" else "❌" if step_status == "failed" else "🔄" if step_status == "running" else "⏳"
                        print(f"  {step_icon} Step {step['step_number']}: {step['title']} ({step_status})")
                
                # Check if completed
                if status in ["success", "failed", "stopped"]:
                    elapsed = int(time.time() - start_time)
                    print(f"\n🏁 Execution completed in {elapsed}s with status: {status}")
                    
                    if status == "success":
                        print("✅ Simulation completed successfully!")
                        show_execution_logs(base_url, execution_id)
                        return True
                    else:
                        print(f"❌ Execution failed: {status_data.get('error_message', 'Unknown error')}")
                        show_execution_logs(base_url, execution_id)
                        return False
                
                time.sleep(2)  # Check every 2 seconds
                
            else:
                print(f"❌ Error getting status: {response.status_code}")
                return False
                
        except KeyboardInterrupt:
            print("\n⏹️ Monitoring interrupted by user")
            return False
        except Exception as e:
            print(f"❌ Error monitoring execution: {e}")
            time.sleep(2)

def show_execution_logs(base_url: str, execution_id: str):
    """Show execution logs."""
    try:
        response = requests.get(f"{base_url}/execution/{execution_id}/logs")
        
        if response.status_code == 200:
            logs_data = response.json()
            logs = logs_data["logs"]
            
            print(f"\n📋 Execution Logs ({len(logs)} entries):")
            print("-" * 40)
            for log in logs[-10:]:  # Show last 10 logs
                print(log)
            print("-" * 40)
            
    except Exception as e:
        print(f"❌ Error getting logs: {e}")

def main():
    """Main test function."""
    print("🎭 ExecuteCmd Simulation Mode Test")
    print("This test runs ExecuteCmd without requiring Docker")
    print("=" * 55)
    
    success = test_executecmd_simulation()
    
    if success:
        print("\n🎉 Simulation test completed successfully!")
        print("ExecuteCmd service is working properly in simulation mode.")
        print("\nTo enable Docker mode:")
        print("1. Install and start Docker Desktop")
        print("2. Run: python test_docker_windows.py")
        print("3. Restart ExecuteCmd service")
    else:
        print("\n💥 Simulation test failed!")
        print("Check the ExecuteCmd service logs for details.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
