import re
import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger("ErrorHandler")

class ErrorType(Enum):
    PACKAGE_MISSING = "package_missing"
    PERMISSION_DENIED = "permission_denied"
    PORT_IN_USE = "port_in_use"
    NETWORK_ERROR = "network_error"
    FILE_NOT_FOUND = "file_not_found"
    COMMAND_NOT_FOUND = "command_not_found"
    PYTHON_ERROR = "python_error"
    NODE_ERROR = "node_error"
    BUILD_ERROR = "build_error"
    DEPENDENCY_ERROR = "dependency_error"
    ENVIRONMENT_ERROR = "environment_error"
    UNKNOWN = "unknown"

@dataclass
class ErrorPattern:
    error_type: ErrorType
    patterns: List[str]
    fix_commands: List[str]
    description: str

class ErrorHandler:
    """
    Sophisticated error detection and recovery system that can identify
    common setup issues and provide appropriate fixes.
    """
    
    def __init__(self):
        self.error_patterns = self._initialize_error_patterns()
    
    def _initialize_error_patterns(self) -> List[ErrorPattern]:
        """Initialize common error patterns and their fixes."""
        return [
            # Package missing errors
            ErrorPattern(
                error_type=ErrorType.PACKAGE_MISSING,
                patterns=[
                    r"E: Unable to locate package (.+)",
                    r"Package '(.+)' has no installation candidate",
                    r"No package '(.+)' found",
                    r"command not found: (.+)",
                    r"bash: (.+): command not found",
                    r"(.+): not found"
                ],
                fix_commands=[
                    "apt-get update",
                    "apt-get install -y {package}"
                ],
                description="Install missing package"
            ),
            
            # Python-specific errors
            ErrorPattern(
                error_type=ErrorType.PYTHON_ERROR,
                patterns=[
                    r"ModuleNotFoundError: No module named '(.+)'",
                    r"ImportError: No module named (.+)",
                    r"pip: command not found",
                    r"python3: command not found",
                    r"python: command not found"
                ],
                fix_commands=[
                    "apt-get update",
                    "apt-get install -y python3 python3-pip python3-venv",
                    "python3 -m pip install --upgrade pip"
                ],
                description="Install Python and pip"
            ),
            
            # Node.js errors
            ErrorPattern(
                error_type=ErrorType.NODE_ERROR,
                patterns=[
                    r"npm: command not found",
                    r"node: command not found",
                    r"npm ERR! missing script: (.+)",
                    r"npm ERR! code ENOENT"
                ],
                fix_commands=[
                    "curl -fsSL https://deb.nodesource.com/setup_18.x | bash -",
                    "apt-get install -y nodejs"
                ],
                description="Install Node.js and npm"
            ),
            
            # Permission errors
            ErrorPattern(
                error_type=ErrorType.PERMISSION_DENIED,
                patterns=[
                    r"Permission denied",
                    r"permission denied",
                    r"EACCES: permission denied",
                    r"Operation not permitted"
                ],
                fix_commands=[
                    "chmod +x {file}",
                    "chown -R root:root ."
                ],
                description="Fix permission issues"
            ),
            
            # Port in use errors
            ErrorPattern(
                error_type=ErrorType.PORT_IN_USE,
                patterns=[
                    r"Port (\d+) is already in use",
                    r"EADDRINUSE.*:(\d+)",
                    r"Address already in use.*:(\d+)"
                ],
                fix_commands=[
                    "lsof -ti:{port} | xargs kill -9 || true",
                    "sleep 2"
                ],
                description="Kill process using the port"
            ),
            
            # Network errors
            ErrorPattern(
                error_type=ErrorType.NETWORK_ERROR,
                patterns=[
                    r"Could not resolve host",
                    r"Network is unreachable",
                    r"Connection timed out",
                    r"Failed to fetch",
                    r"Unable to connect to"
                ],
                fix_commands=[
                    "echo 'nameserver *******' > /etc/resolv.conf",
                    "apt-get update --fix-missing"
                ],
                description="Fix network connectivity"
            ),
            
            # File not found errors
            ErrorPattern(
                error_type=ErrorType.FILE_NOT_FOUND,
                patterns=[
                    r"No such file or directory: '(.+)'",
                    r"cannot access '(.+)': No such file or directory",
                    r"FileNotFoundError: \[Errno 2\] No such file or directory: '(.+)'"
                ],
                fix_commands=[
                    "mkdir -p {directory}",
                    "touch {file}"
                ],
                description="Create missing file or directory"
            ),
            
            # Git errors
            ErrorPattern(
                error_type=ErrorType.COMMAND_NOT_FOUND,
                patterns=[
                    r"git: command not found",
                    r"fatal: not a git repository"
                ],
                fix_commands=[
                    "apt-get update",
                    "apt-get install -y git"
                ],
                description="Install Git"
            ),
            
            # Build errors
            ErrorPattern(
                error_type=ErrorType.BUILD_ERROR,
                patterns=[
                    r"make: command not found",
                    r"gcc: command not found",
                    r"g\+\+: command not found",
                    r"error: Microsoft Visual C\+\+ 14.0 is required"
                ],
                fix_commands=[
                    "apt-get update",
                    "apt-get install -y build-essential"
                ],
                description="Install build tools"
            ),
            
            # Dependency errors
            ErrorPattern(
                error_type=ErrorType.DEPENDENCY_ERROR,
                patterns=[
                    r"requirements.txt: No such file",
                    r"package.json: No such file",
                    r"The following packages have unmet dependencies"
                ],
                fix_commands=[
                    "apt-get update",
                    "apt-get install -f"
                ],
                description="Fix dependency issues"
            ),
            
            # Environment errors
            ErrorPattern(
                error_type=ErrorType.ENVIRONMENT_ERROR,
                patterns=[
                    r"DEBIAN_FRONTEND: command not found",
                    r"locale: Cannot set LC_ALL",
                    r"perl: warning: Setting locale failed"
                ],
                fix_commands=[
                    "export DEBIAN_FRONTEND=noninteractive",
                    "apt-get update",
                    "apt-get install -y locales",
                    "locale-gen en_US.UTF-8"
                ],
                description="Fix environment and locale issues"
            )
        ]
    
    def analyze_error(self, error_output: str, command: str, exit_code: int) -> Optional[Tuple[ErrorType, List[str], str]]:
        """
        Analyze error output and return error type, fix commands, and description.
        
        Returns:
            Tuple of (error_type, fix_commands, description) or None if no pattern matches
        """
        logger.info(f"Analyzing error for command: {command}")
        logger.debug(f"Error output: {error_output[:500]}...")
        
        for pattern_group in self.error_patterns:
            for pattern in pattern_group.patterns:
                match = re.search(pattern, error_output, re.IGNORECASE | re.MULTILINE)
                if match:
                    logger.info(f"Matched error pattern: {pattern_group.error_type.value}")
                    
                    # Extract matched groups for dynamic fix commands
                    fix_commands = self._customize_fix_commands(
                        pattern_group.fix_commands, 
                        match, 
                        command, 
                        error_output
                    )
                    
                    return pattern_group.error_type, fix_commands, pattern_group.description
        
        logger.warning("No error pattern matched")
        return None
    
    def _customize_fix_commands(self, fix_commands: List[str], match: re.Match, 
                              original_command: str, error_output: str) -> List[str]:
        """Customize fix commands based on the specific error context."""
        customized_commands = []
        
        for command in fix_commands:
            # Replace placeholders with actual values
            if "{package}" in command:
                package = self._extract_package_name(match, original_command, error_output)
                command = command.replace("{package}", package)
            
            if "{port}" in command:
                port = self._extract_port_number(match, error_output)
                command = command.replace("{port}", port)
            
            if "{file}" in command:
                file_path = self._extract_file_path(match, error_output)
                command = command.replace("{file}", file_path)
            
            if "{directory}" in command:
                directory = self._extract_directory_path(match, error_output)
                command = command.replace("{directory}", directory)
            
            customized_commands.append(command)
        
        return customized_commands
    
    def _extract_package_name(self, match: re.Match, original_command: str, error_output: str) -> str:
        """Extract package name from error context."""
        if match.groups():
            return match.group(1).strip()
        
        # Try to extract from original command
        if "install" in original_command:
            parts = original_command.split()
            if "install" in parts:
                install_idx = parts.index("install")
                if install_idx + 1 < len(parts):
                    return parts[install_idx + 1]
        
        # Common packages based on command
        command_packages = {
            "git": "git",
            "curl": "curl",
            "wget": "wget",
            "make": "build-essential",
            "gcc": "build-essential",
            "python3": "python3",
            "pip": "python3-pip",
            "node": "nodejs",
            "npm": "nodejs"
        }
        
        for cmd, pkg in command_packages.items():
            if cmd in original_command.lower():
                return pkg
        
        return "unknown-package"
    
    def _extract_port_number(self, match: re.Match, error_output: str) -> str:
        """Extract port number from error context."""
        if match.groups():
            return match.group(1)
        
        # Look for port numbers in error output
        port_match = re.search(r':(\d+)', error_output)
        if port_match:
            return port_match.group(1)
        
        return "8000"  # Default port
    
    def _extract_file_path(self, match: re.Match, error_output: str) -> str:
        """Extract file path from error context."""
        if match.groups():
            return match.group(1)
        
        return "missing-file"
    
    def _extract_directory_path(self, match: re.Match, error_output: str) -> str:
        """Extract directory path from error context."""
        if match.groups():
            file_path = match.group(1)
            # Extract directory from file path
            if "/" in file_path:
                return "/".join(file_path.split("/")[:-1])
            return "."
        
        return "missing-directory"
    
    def get_common_fixes(self) -> Dict[str, List[str]]:
        """Get a dictionary of common fixes for different scenarios."""
        return {
            "python_setup": [
                "apt-get update",
                "apt-get install -y python3 python3-pip python3-venv",
                "python3 -m pip install --upgrade pip"
            ],
            "nodejs_setup": [
                "curl -fsSL https://deb.nodesource.com/setup_18.x | bash -",
                "apt-get install -y nodejs"
            ],
            "build_tools": [
                "apt-get update",
                "apt-get install -y build-essential"
            ],
            "git_setup": [
                "apt-get update",
                "apt-get install -y git"
            ],
            "network_fix": [
                "echo 'nameserver *******' > /etc/resolv.conf",
                "apt-get update --fix-missing"
            ]
        }
