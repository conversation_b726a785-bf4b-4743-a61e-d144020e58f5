#!/usr/bin/env python3
"""
Integration example showing how to use ExecuteCmd with RunRepo service.
This demonstrates the complete workflow from setup chain generation to execution.
"""

import requests
import json
import time
import sys
from typing import Dict, Any, Optional

class MicroserviceIntegration:
    def __init__(self, runrepo_url: str = "http://localhost:8004", 
                 executecmd_url: str = "http://localhost:8005"):
        self.runrepo_url = runrepo_url
        self.executecmd_url = executecmd_url
        self.session = requests.Session()
    
    def check_services_health(self) -> bool:
        """Check if both services are healthy."""
        print("🔍 Checking service health...")
        
        # Check RunRepo service
        try:
            response = self.session.get(f"{self.runrepo_url}/health")
            if response.status_code == 200:
                print("✅ RunRepo service is healthy")
            else:
                print(f"❌ RunRepo service unhealthy: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ RunRepo service error: {e}")
            return False
        
        # Check ExecuteCmd service
        try:
            response = self.session.get(f"{self.executecmd_url}/health")
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ ExecuteCmd service is healthy")
                print(f"   Docker available: {health_data.get('docker_available', False)}")
                print(f"   AI available: {health_data.get('ai_available', False)}")
            else:
                print(f"❌ ExecuteCmd service unhealthy: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ ExecuteCmd service error: {e}")
            return False
        
        return True
    
    def generate_setup_chain(self, repository_url: str, files_analyzed: list) -> Optional[Dict]:
        """Generate setup chain using RunRepo service."""
        print(f"🔧 Generating setup chain for {repository_url}...")
        
        payload = {
            "repository_url": repository_url,
            "files_analyzed": files_analyzed
        }
        
        try:
            response = self.session.post(
                f"{self.runrepo_url}/generate-setup-chain?use_ai=true",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                setup_data = response.json()
                print(f"✅ Setup chain generated with {len(setup_data['setup_chain'])} steps")
                print(f"   Estimated time: {setup_data.get('total_estimated_time', 'Unknown')}")
                print(f"   Architecture: {setup_data.get('architecture_overview', 'Unknown')}")
                return setup_data
            else:
                print(f"❌ Failed to generate setup chain: {response.status_code}")
                print(f"Error: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error generating setup chain: {e}")
            return None
    
    def execute_setup_chain(self, repository_url: str, setup_chain: list, 
                          execution_options: Dict = None) -> Optional[str]:
        """Execute setup chain using ExecuteCmd service."""
        print(f"🚀 Executing setup chain for {repository_url}...")
        
        payload = {
            "repository_url": repository_url,
            "setup_chain": setup_chain,
            "execution_options": execution_options or {}
        }
        
        try:
            response = self.session.post(
                f"{self.executecmd_url}/execute-setup-chain",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                execution_id = result["execution_id"]
                print(f"✅ Execution started: {execution_id}")
                print(f"📊 Status: {result['status']}")
                print(f"🐳 Container: {result.get('container_id', 'N/A')}")
                return execution_id
            else:
                print(f"❌ Failed to start execution: {response.status_code}")
                print(f"Error: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error starting execution: {e}")
            return None
    
    def monitor_execution(self, execution_id: str) -> bool:
        """Monitor execution progress."""
        print(f"\n📊 Monitoring execution {execution_id}...")
        
        start_time = time.time()
        last_step = 0
        
        while True:
            try:
                response = self.session.get(f"{self.executecmd_url}/execution/{execution_id}/status")
                
                if response.status_code == 200:
                    status_data = response.json()
                    status = status_data["status"]
                    progress = status_data["progress"]
                    current_step = progress['current_step']
                    
                    # Show progress update
                    elapsed = int(time.time() - start_time)
                    print(f"[{elapsed:03d}s] Status: {status} | Step: {current_step}/{progress['total_steps']} ({progress['percentage']}%)")
                    
                    # Show new step details
                    if current_step > last_step and "steps" in status_data:
                        for step in status_data["steps"]:
                            if step["step_number"] == current_step:
                                print(f"  🔄 Executing: {step['title']}")
                                break
                        last_step = current_step
                    
                    # Show any errors or fixes
                    if status_data.get("fixes_applied"):
                        recent_fixes = status_data["fixes_applied"][-1:]  # Show only latest fix
                        for fix in recent_fixes:
                            print(f"  🔧 Applied fix: {fix}")
                    
                    # Check if completed
                    if status in ["success", "failed", "stopped"]:
                        elapsed = int(time.time() - start_time)
                        print(f"\n🏁 Execution completed in {elapsed}s with status: {status}")
                        
                        if status == "success":
                            print("✅ Setup chain executed successfully!")
                            self._show_execution_summary(execution_id)
                            return True
                        else:
                            print(f"❌ Execution failed: {status_data.get('error_message', 'Unknown error')}")
                            self._show_execution_summary(execution_id)
                            return False
                    
                    time.sleep(3)  # Check every 3 seconds
                    
                else:
                    print(f"❌ Error getting status: {response.status_code}")
                    return False
                    
            except KeyboardInterrupt:
                print("\n⏹️ Monitoring interrupted by user")
                return False
            except Exception as e:
                print(f"❌ Error monitoring execution: {e}")
                time.sleep(5)
    
    def _show_execution_summary(self, execution_id: str) -> None:
        """Show execution summary."""
        try:
            response = self.session.get(f"{self.executecmd_url}/execution/{execution_id}/details")
            
            if response.status_code == 200:
                details = response.json()
                
                print(f"\n📋 Execution Summary:")
                print(f"   Execution ID: {execution_id}")
                print(f"   Total Steps: {details['total_steps']}")
                print(f"   Fixes Applied: {len(details.get('fixes_applied', []))}")
                
                # Show step results
                print(f"\n📊 Step Results:")
                for step in details['steps']:
                    status_icon = "✅" if step['status'] == 'success' else "❌" if step['status'] == 'failed' else "⏳"
                    retry_info = f" (retried {step['retry_count']}x)" if step['retry_count'] > 0 else ""
                    print(f"   {status_icon} Step {step['step_number']}: {step['title']}{retry_info}")
                
                # Show applied fixes
                if details.get('fixes_applied'):
                    print(f"\n🔧 Applied Fixes:")
                    for fix in details['fixes_applied']:
                        print(f"   - {fix}")
                        
        except Exception as e:
            print(f"❌ Error getting execution summary: {e}")
    
    def run_complete_workflow(self, repository_url: str) -> bool:
        """Run the complete workflow from setup generation to execution."""
        print("🌟 Starting Complete Microservice Workflow")
        print("=" * 50)
        
        # Sample analyzed files (normally would come from ReadFiles service)
        sample_files_analyzed = [
            {
                "path": "app.py",
                "content": "from flask import Flask\napp = Flask(__name__)\n\nif __name__ == '__main__':\n    app.run()",
                "analysis": "Flask web application with basic setup"
            },
            {
                "path": "requirements.txt", 
                "content": "Flask==3.0.0\ngoogle-generativeai==0.8.3\npython-dotenv==1.0.0",
                "analysis": "Python dependencies including Flask and AI libraries"
            }
        ]
        
        # Step 1: Check services
        if not self.check_services_health():
            return False
        
        # Step 2: Generate setup chain
        setup_data = self.generate_setup_chain(repository_url, sample_files_analyzed)
        if not setup_data:
            return False
        
        # Step 3: Execute setup chain
        execution_options = {
            "base_image": "ubuntu:22.04",
            "timeout": 1800,
            "auto_fix": True,
            "max_retries_per_step": 3
        }
        
        execution_id = self.execute_setup_chain(
            repository_url, 
            setup_data['setup_chain'], 
            execution_options
        )
        
        if not execution_id:
            return False
        
        # Step 4: Monitor execution
        success = self.monitor_execution(execution_id)
        
        print(f"\n🎯 Workflow Result: {'SUCCESS' if success else 'FAILED'}")
        return success

def main():
    """Main function to run the integration example."""
    print("🔗 ExecuteCmd + RunRepo Integration Example")
    print("=" * 45)
    
    # Repository to test with
    repository_url = "https://github.com/AliSoua/HR-Management"
    
    # Create integration instance
    integration = MicroserviceIntegration()
    
    # Run complete workflow
    success = integration.run_complete_workflow(repository_url)
    
    if success:
        print("\n🎉 Integration test completed successfully!")
        print("The repository has been automatically set up and is ready to run!")
    else:
        print("\n💥 Integration test failed!")
        print("Check the logs above for details on what went wrong.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
