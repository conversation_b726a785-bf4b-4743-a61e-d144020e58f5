#!/usr/bin/env python3
"""
Test ExecuteCmd working directory handling fix.
This test specifically checks if the working directory issue is resolved.
"""

import requests
import json
import time
import sys

def test_working_directory_fix():
    """Test ExecuteCmd with working directory fix."""
    base_url = "http://localhost:8005"
    
    print("📁 Working Directory Fix Test")
    print("This test verifies that working directory handling is fixed")
    print("=" * 60)
    
    # Simple setup chain that tests working directory changes
    payload = {
        "repository_url": "https://github.com/AliSoua/HR-Management",
        "setup_chain": [
            {
                "commands": [
                    "pwd",
                    "ls -la"
                ],
                "description": "Check initial working directory",
                "estimated_time": "10 seconds",
                "notes": ["Should be in /workspace"],
                "prerequisites": [],
                "step_number": 1,
                "title": "Check Initial Directory",
                "working_directory": "~/"
            },
            {
                "commands": [
                    "git clone https://github.com/AliSoua/HR-Management.git",
                    "ls -la",
                    "ls -la HR-Management"
                ],
                "description": "Clone repository and verify structure",
                "estimated_time": "1-2 minutes",
                "notes": ["Should create HR-Management directory"],
                "prerequisites": ["Git installed"],
                "step_number": 2,
                "title": "Clone Repository",
                "working_directory": "~/"
            },
            {
                "commands": [
                    "pwd",
                    "ls -la",
                    "python3 -m venv venv",
                    "ls -la"
                ],
                "description": "Create virtual environment in project directory",
                "estimated_time": "1 minute",
                "notes": ["Should work in HR-Management directory"],
                "prerequisites": ["Repository cloned"],
                "step_number": 3,
                "title": "Create Virtual Environment",
                "working_directory": "./HR-Management"
            }
        ],
        "execution_options": {
            "base_image": "ubuntu:22.04",
            "timeout": 1800,
            "auto_fix": True,
            "max_retries_per_step": 2,
            "simulation_mode": True  # Use simulation to avoid Docker issues
        }
    }
    
    print(f"📋 Test Details:")
    print(f"   Repository: {payload['repository_url']}")
    print(f"   Steps: {len(payload['setup_chain'])}")
    print(f"   Mode: Simulation (to test logic)")
    print(f"   Focus: Working directory handling")
    
    # Test health first
    print(f"\n1. Testing service health...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Service is healthy")
            print(f"   Docker available: {health_data.get('docker_available', False)}")
            print(f"   AI available: {health_data.get('ai_available', False)}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # Start execution
    print(f"\n2. Starting execution with working directory test...")
    try:
        response = requests.post(
            f"{base_url}/execute-setup-chain",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            execution_id = result["execution_id"]
            print(f"✅ Execution started: {execution_id}")
            print(f"📊 Status: {result['status']}")
            print(f"🎭 Container: {result.get('container_id', 'N/A')} (simulated)")
            
            # Monitor execution
            return monitor_execution(base_url, execution_id)
        else:
            print(f"❌ Failed to start execution: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error starting execution: {e}")
        return False

def monitor_execution(base_url: str, execution_id: str) -> bool:
    """Monitor execution progress focusing on working directory issues."""
    print(f"\n3. Monitoring execution {execution_id}...")
    print("=" * 50)
    
    start_time = time.time()
    last_step = 0
    
    while True:
        try:
            response = requests.get(f"{base_url}/execution/{execution_id}/status")
            
            if response.status_code == 200:
                status_data = response.json()
                status = status_data["status"]
                progress = status_data["progress"]
                current_step = progress['current_step']
                
                elapsed = int(time.time() - start_time)
                print(f"[{elapsed:03d}s] Status: {status} | Step: {current_step}/{progress['total_steps']} ({progress['percentage']}%)")
                
                # Show new step details
                if current_step > last_step and "steps" in status_data:
                    for step in status_data["steps"]:
                        if step["step_number"] == current_step:
                            print(f"  🎭 Simulating: {step['title']}")
                            break
                    last_step = current_step
                
                # Show step status with focus on working directory issues
                if "steps" in status_data:
                    for step in status_data["steps"]:
                        step_status = step["status"]
                        step_icon = "✅" if step_status == "success" else "❌" if step_status == "failed" else "🎭" if step_status == "running" else "🔧" if step_status == "error_recovery" else "⏳"
                        
                        if step["step_number"] <= current_step:
                            print(f"     {step_icon} Step {step['step_number']}: {step['title']} ({step_status})")
                            
                            # Check for working directory errors
                            if step.get("has_error") and step.get("error_preview"):
                                if "No such file or directory" in step["error_preview"]:
                                    print(f"       ⚠️ Working directory issue detected!")
                                elif "cd:" in step["error_preview"]:
                                    print(f"       ⚠️ Directory change issue detected!")
                
                # Check if completed
                if status in ["success", "failed", "stopped"]:
                    elapsed = int(time.time() - start_time)
                    print(f"\n🏁 Execution completed in {elapsed}s with status: {status}")
                    
                    if status == "success":
                        print("✅ Working directory handling test passed!")
                        show_working_directory_analysis(base_url, execution_id)
                        return True
                    else:
                        print(f"❌ Working directory test failed: {status_data.get('error_message', 'Unknown error')}")
                        show_working_directory_analysis(base_url, execution_id)
                        return False
                
                time.sleep(2)  # Check every 2 seconds
                
            else:
                print(f"❌ Error getting status: {response.status_code}")
                return False
                
        except KeyboardInterrupt:
            print("\n⏹️ Monitoring interrupted by user")
            return False
        except Exception as e:
            print(f"❌ Error monitoring execution: {e}")
            time.sleep(3)

def show_working_directory_analysis(base_url: str, execution_id: str):
    """Show analysis of working directory handling."""
    try:
        response = requests.get(f"{base_url}/execution/{execution_id}/logs")
        
        if response.status_code == 200:
            logs_data = response.json()
            logs = logs_data["logs"]
            
            print(f"\n📁 Working Directory Analysis:")
            print("-" * 50)
            
            working_dir_logs = []
            for log in logs:
                if any(keyword in log.lower() for keyword in ['cd ', 'pwd', 'directory', 'workspace', 'hr-management']):
                    working_dir_logs.append(log)
            
            if working_dir_logs:
                print("Working directory related logs:")
                for log in working_dir_logs:
                    print(f"   {log}")
            else:
                print("No specific working directory issues found in logs")
            
            print("-" * 50)
            
    except Exception as e:
        print(f"❌ Error getting working directory analysis: {e}")

def main():
    """Main test function."""
    print("🧪 ExecuteCmd Working Directory Fix Test")
    print("Tests the fix for working directory handling issues")
    print("=" * 60)
    
    success = test_working_directory_fix()
    
    if success:
        print("\n🎉 Working directory fix test passed!")
        print("The service can now handle working directory changes correctly.")
        print("\nNext: Try the Docker fallback test again!")
    else:
        print("\n💥 Working directory fix test failed!")
        print("The working directory issue still needs attention.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
