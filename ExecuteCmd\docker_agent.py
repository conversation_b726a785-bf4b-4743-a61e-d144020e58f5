import os
import logging
import json
import time
import docker
import threading
from typing import Dict, List, Any, Optional, Callable
import google.generativeai as genai
from dataclasses import dataclass
from enum import Enum
import re

logger = logging.getLogger("DockerAgent")

class ExecutionStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    STOPPED = "stopped"
    ERROR_RECOVERY = "error_recovery"

@dataclass
class ExecutionStep:
    step_number: int
    title: str
    commands: List[str]
    working_directory: str
    status: ExecutionStatus = ExecutionStatus.PENDING
    output: str = ""
    error: str = ""
    retry_count: int = 0
    max_retries: int = 3

@dataclass
class ExecutionResult:
    execution_id: str
    status: ExecutionStatus
    container_id: Optional[str]
    current_step: int
    total_steps: int
    steps: List[ExecutionStep]
    logs: List[str]
    error_message: Optional[str] = None
    fixes_applied: List[str] = None

class DockerExecutionAgent:
    """
    AI-powered Docker execution agent that runs setup chains in containers
    and automatically handles errors and fixes issues.
    """
    
    def __init__(self, execution_id: str, repository_url: str, setup_chain: List[Dict], 
                 execution_options: Dict = None, progress_callback: Callable = None):
        self.execution_id = execution_id
        self.repository_url = repository_url
        self.setup_chain = setup_chain
        self.execution_options = execution_options or {}
        self.progress_callback = progress_callback
        
        # Docker client
        self.docker_client = docker.from_env()
        self.container = None
        
        # AI model for error handling
        self.gemini_model = None
        self._initialize_gemini()
        
        # Execution state
        self.execution_result = ExecutionResult(
            execution_id=execution_id,
            status=ExecutionStatus.PENDING,
            container_id=None,
            current_step=0,
            total_steps=len(setup_chain),
            steps=[],
            logs=[],
            fixes_applied=[]
        )
        
        # Convert setup chain to execution steps
        self._prepare_execution_steps()
        
        # Execution control
        self._stop_requested = False
        self._execution_thread = None
    
    def _initialize_gemini(self):
        """Initialize the Gemini AI model for error handling."""
        try:
            api_key = os.getenv('GEMINI_API_KEY')
            if not api_key:
                logger.warning("GEMINI_API_KEY not found. AI error recovery will be disabled.")
                return
            
            genai.configure(api_key=api_key)
            self.gemini_model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')
            logger.info("Gemini AI model initialized for error recovery.")
            
        except Exception as e:
            logger.error(f"Failed to initialize Gemini AI: {e}")
            self.gemini_model = None
    
    def _prepare_execution_steps(self):
        """Convert setup chain to execution steps."""
        for i, step_data in enumerate(self.setup_chain):
            step = ExecutionStep(
                step_number=step_data.get('step_number', i + 1),
                title=step_data.get('title', f'Step {i + 1}'),
                commands=step_data.get('commands', []),
                working_directory=step_data.get('working_directory', './'),
                max_retries=self.execution_options.get('max_retries_per_step', 3)
            )
            self.execution_result.steps.append(step)
    
    def start_execution(self) -> str:
        """Start the execution in a separate thread."""
        if self._execution_thread and self._execution_thread.is_alive():
            raise RuntimeError("Execution already in progress")
        
        self._execution_thread = threading.Thread(target=self._execute_setup_chain)
        self._execution_thread.daemon = True
        self._execution_thread.start()
        
        return self.execution_id
    
    def stop_execution(self):
        """Stop the execution."""
        self._stop_requested = True
        if self.container:
            try:
                self.container.stop(timeout=10)
                logger.info(f"Container {self.container.id} stopped.")
            except Exception as e:
                logger.error(f"Error stopping container: {e}")
        
        self.execution_result.status = ExecutionStatus.STOPPED
        self._notify_progress()
    
    def get_status(self) -> ExecutionResult:
        """Get current execution status."""
        return self.execution_result
    
    def _execute_setup_chain(self):
        """Main execution method that runs in a separate thread."""
        try:
            self.execution_result.status = ExecutionStatus.RUNNING
            self._notify_progress()
            
            # Create and start container
            self._create_container()
            
            # Execute each step
            for i, step in enumerate(self.execution_result.steps):
                if self._stop_requested:
                    break
                
                self.execution_result.current_step = i + 1
                self._execute_step(step)
                
                if step.status == ExecutionStatus.FAILED:
                    self.execution_result.status = ExecutionStatus.FAILED
                    self.execution_result.error_message = f"Step {step.step_number} failed: {step.error}"
                    break
            
            # Check final status
            if not self._stop_requested and self.execution_result.status == ExecutionStatus.RUNNING:
                self.execution_result.status = ExecutionStatus.SUCCESS
                self._log("✅ Setup chain executed successfully!")
            
        except Exception as e:
            logger.error(f"Execution failed: {e}", exc_info=True)
            self.execution_result.status = ExecutionStatus.FAILED
            self.execution_result.error_message = str(e)
        
        finally:
            self._cleanup_container()
            self._notify_progress()
    
    def _create_container(self):
        """Create and start the Docker container."""
        base_image = self.execution_options.get('base_image', 'ubuntu:22.04')
        
        self._log(f"🐳 Creating container with base image: {base_image}")
        
        # Container configuration
        container_config = {
            'image': base_image,
            'command': '/bin/bash',
            'stdin_open': True,
            'tty': True,
            'detach': True,
            'working_dir': '/workspace',
            'environment': {
                'DEBIAN_FRONTEND': 'noninteractive',
                'PYTHONUNBUFFERED': '1'
            }
        }
        
        # Create container
        self.container = self.docker_client.containers.run(**container_config)
        self.execution_result.container_id = self.container.id
        
        self._log(f"📦 Container created: {self.container.id[:12]}")
        
        # Install basic tools
        self._execute_container_command("apt-get update && apt-get install -y git curl wget python3 python3-pip nodejs npm")
    
    def _execute_step(self, step: ExecutionStep):
        """Execute a single step with error handling and recovery."""
        step.status = ExecutionStatus.RUNNING
        self._notify_progress()
        
        self._log(f"🔄 Executing Step {step.step_number}: {step.title}")
        
        for command in step.commands:
            if self._stop_requested:
                return
            
            # Skip comments and empty lines
            if not command.strip() or command.strip().startswith('#'):
                continue
            
            success = self._execute_command_with_retry(step, command)
            if not success:
                step.status = ExecutionStatus.FAILED
                return
        
        step.status = ExecutionStatus.SUCCESS
        self._log(f"✅ Step {step.step_number} completed successfully")
        self._notify_progress()

    def _execute_command_with_retry(self, step: ExecutionStep, command: str) -> bool:
        """Execute a command with automatic retry and error recovery."""
        for attempt in range(step.max_retries + 1):
            try:
                self._log(f"💻 Executing: {command}")

                # Change to working directory if specified
                if step.working_directory != './':
                    cd_command = f"cd {step.working_directory} && {command}"
                else:
                    cd_command = command

                exit_code, output = self._execute_container_command(cd_command)

                if exit_code == 0:
                    step.output += output + "\n"
                    return True
                else:
                    error_msg = f"Command failed with exit code {exit_code}: {output}"
                    step.error += error_msg + "\n"
                    self._log(f"❌ {error_msg}")

                    # Try AI-powered error recovery
                    if attempt < step.max_retries and self.gemini_model:
                        self._log(f"🤖 Attempting AI-powered error recovery (attempt {attempt + 1}/{step.max_retries})")
                        recovery_success = self._attempt_error_recovery(step, command, output, exit_code)
                        if recovery_success:
                            step.retry_count += 1
                            continue

                    step.retry_count = attempt + 1
                    return False

            except Exception as e:
                error_msg = f"Exception executing command: {str(e)}"
                step.error += error_msg + "\n"
                self._log(f"❌ {error_msg}")

                if attempt < step.max_retries:
                    self._log(f"🔄 Retrying command (attempt {attempt + 2}/{step.max_retries + 1})")
                    time.sleep(2)
                else:
                    step.retry_count = attempt + 1
                    return False

        return False

    def _execute_container_command(self, command: str) -> tuple[int, str]:
        """Execute a command in the container and return exit code and output."""
        if not self.container:
            raise RuntimeError("Container not available")

        try:
            exec_result = self.container.exec_run(
                f'/bin/bash -c "{command}"',
                stdout=True,
                stderr=True,
                stream=False
            )

            output = exec_result.output.decode('utf-8', errors='replace')
            return exec_result.exit_code, output

        except Exception as e:
            logger.error(f"Error executing container command: {e}")
            return 1, str(e)

    def _attempt_error_recovery(self, step: ExecutionStep, failed_command: str,
                              error_output: str, exit_code: int) -> bool:
        """Use AI to analyze the error and attempt recovery."""
        if not self.gemini_model:
            return False

        try:
            step.status = ExecutionStatus.ERROR_RECOVERY
            self._notify_progress()

            # Prepare context for AI
            context = self._prepare_error_context(step, failed_command, error_output, exit_code)

            prompt = f"""
You are an expert DevOps engineer helping to fix a command execution error in a Docker container.

CONTEXT:
{context}

FAILED COMMAND: {failed_command}
EXIT CODE: {exit_code}
ERROR OUTPUT:
{error_output}

Your task is to analyze this error and provide specific fix commands that should be executed before retrying the original command.

Respond with a JSON object in this format:
{{
  "analysis": "Brief analysis of what went wrong",
  "fix_commands": ["command1", "command2", "command3"],
  "explanation": "Explanation of what the fix commands do"
}}

Common issues and fixes:
- Missing packages: Install them with apt-get or package manager
- Permission errors: Use sudo or change ownership
- Missing directories: Create them with mkdir -p
- Port conflicts: Kill processes or use different ports
- Network issues: Check connectivity or use different sources
- Python/Node.js issues: Install correct versions or dependencies

Be specific and practical. Only include commands that are likely to fix the issue.
"""

            response = self.gemini_model.generate_content(prompt)
            response_text = response.text.strip()

            # Parse AI response
            json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
            if json_match:
                json_text = json_match.group(1)
            else:
                json_text = response_text

            try:
                fix_data = json.loads(json_text)
                fix_commands = fix_data.get('fix_commands', [])
                analysis = fix_data.get('analysis', 'AI analysis not available')
                explanation = fix_data.get('explanation', 'No explanation provided')

                self._log(f"🤖 AI Analysis: {analysis}")
                self._log(f"🔧 Applying fix: {explanation}")

                # Execute fix commands
                for fix_command in fix_commands:
                    self._log(f"🛠️ Fix command: {fix_command}")
                    exit_code, output = self._execute_container_command(fix_command)

                    if exit_code != 0:
                        self._log(f"❌ Fix command failed: {output}")
                        return False
                    else:
                        self._log(f"✅ Fix command succeeded")

                # Record the fix
                fix_description = f"Step {step.step_number}: {explanation}"
                self.execution_result.fixes_applied.append(fix_description)

                return True

            except json.JSONDecodeError as e:
                self._log(f"❌ Failed to parse AI response: {e}")
                return False

        except Exception as e:
            logger.error(f"Error in AI recovery: {e}")
            return False

        finally:
            step.status = ExecutionStatus.RUNNING

    def _prepare_error_context(self, step: ExecutionStep, failed_command: str,
                             error_output: str, exit_code: int) -> str:
        """Prepare context information for AI error analysis."""
        context_parts = [
            f"Repository: {self.repository_url}",
            f"Step: {step.step_number} - {step.title}",
            f"Working Directory: {step.working_directory}",
            f"Previous Commands in Step: {step.commands[:step.commands.index(failed_command)] if failed_command in step.commands else 'N/A'}",
            f"Container Base Image: {self.execution_options.get('base_image', 'ubuntu:22.04')}",
            f"Exit Code: {exit_code}",
            f"Error Output: {error_output[:1000]}{'...' if len(error_output) > 1000 else ''}",
        ]

        # Add recent logs for context
        recent_logs = self.execution_result.logs[-10:] if len(self.execution_result.logs) > 10 else self.execution_result.logs
        if recent_logs:
            context_parts.append(f"Recent Execution Logs:\n" + "\n".join(recent_logs))

        return "\n".join(context_parts)

    def _cleanup_container(self):
        """Clean up the Docker container."""
        if self.container:
            try:
                self.container.remove(force=True)
                self._log(f"🗑️ Container {self.container.id[:12]} cleaned up")
            except Exception as e:
                logger.error(f"Error cleaning up container: {e}")

    def _log(self, message: str):
        """Add a log message."""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.execution_result.logs.append(log_entry)
        logger.info(log_entry)

    def _notify_progress(self):
        """Notify progress callback if available."""
        if self.progress_callback:
            try:
                self.progress_callback(self.execution_result)
            except Exception as e:
                logger.error(f"Error in progress callback: {e}")
