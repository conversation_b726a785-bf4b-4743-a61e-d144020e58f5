/* --- Dark Theme Color Palette --- */
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  
  /* Background Colors */
  --bg-primary: #121212; /* Main page background */
  --bg-secondary: #1e1e1e; /* UI elements like cards, inputs */

  /* Text Colors */
  --text-primary: rgba(255, 255, 255, 0.87);
  --text-secondary: rgba(255, 255, 255, 0.6);

  /* Border and Divider Colors */
  --border-color: rgba(255, 255, 255, 0.12);

  /* Accent Colors */
  --primary-accent: #768fff;
  --primary-accent-hover: #8f9fff;
  --success-accent: #66bb6a;
  --danger-accent: #ef5350;
  --running-accent: #ffa726;
}

/* --- Base Styles --- */
body {
  margin: 0;
  display: flex;
  justify-content: center;
  padding-top: 50px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.container {
  width: 90%;
  max-width: 900px;
  text-align: center;
}

h1 {
  color: var(--text-primary);
}

/* --- Form Elements --- */
form {
  display: flex;
  gap: 10px;
  margin-bottom: 2rem;
}

input[type="url"] {
  flex-grow: 1;
  padding: 0.8rem;
  font-size: 1rem;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: border-color 0.25s;
}

input[type="url"]:focus {
  outline: none;
  border-color: var(--primary-accent);
}

/* --- Buttons --- */
button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: var(--primary-accent);
  color: #000; /* Dark text on bright accent for contrast */
  cursor: pointer;
  transition: background-color 0.25s;
}
button:hover {
  background-color: var(--primary-accent-hover);
}
button:disabled {
  background-color: #333;
  color: #777;
  cursor: not-allowed;
}

.close-btn {
  background-color: var(--danger-accent);
  color: white;
}
.close-btn:hover {
  background-color: #f67a78;
}

/* --- Status & Result Display --- */
.status-box {
  margin-top: 2rem;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: left;
  border: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.status-header strong {
  font-size: 1.2rem;
}

/* Dynamic status colors */
.status-box.running .status-header strong { color: var(--running-accent); }
.status-box.completed .status-header strong { color: var(--success-accent); }
.status-box.failed .status-header strong { color: var(--danger-accent); }
.status-box.creating .status-header strong { color: var(--text-secondary); }

.result-area {
  margin-top: 1rem;
  max-height: 60vh;
  overflow: auto;
  background-color: #0d0d0d; /* Even darker for code blocks */
  border-radius: 4px;
}

pre {
  margin: 0;
  padding: 1rem;
  white-space: pre-wrap;
  word-break: break-all;
  color: #e0e0e0; /* Light gray for JSON text */
  font-family: "Fira Code", "Courier New", Courier, monospace;
}

/* --- Styles for the Analysis Box --- */
.analysis-box {
  margin-top: 2rem;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: left;
  border: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.analysis-box.identifying .status-header strong { color: var(--running-accent); }
.analysis-box.completed .status-header strong { color: var(--success-accent); }
.analysis-box.failed .status-header strong { color: var(--danger-accent); }

/* --- Analysis Settings --- */
.analysis-settings {
  margin: 1.5rem 0;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  text-align: left;
}

.toggle-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  margin-bottom: 0.5rem;
}

.toggle-container input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-accent);
}

.toggle-label {
  font-weight: 500;
  color: var(--text-primary);
}

.toggle-description {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* --- Enhanced Analysis Results --- */
.analysis-results {
  color: var(--text-primary);
}

.analysis-metadata {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.analysis-method, .files-count {
  font-size: 0.95rem;
}

.files-list h3 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.file-item {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
  border-left: 3px solid var(--primary-accent);
}

.file-path {
  margin-bottom: 0.5rem;
}

.file-path code {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  font-family: "Fira Code", "Courier New", Courier, monospace;
  color: var(--primary-accent);
  font-size: 0.9rem;
}

.file-reason {
  color: var(--text-secondary);
  line-height: 1.5;
  font-size: 0.95rem;
}

/* --- File Reading Results Styles --- */
.file-reading-box {
  margin-top: 2rem;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: left;
  border: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.file-reading-box.reading .status-header strong { color: var(--running-accent); }
.file-reading-box.completed .status-header strong { color: var(--success-accent); }
.file-reading-box.failed .status-header strong { color: var(--danger-accent); }

.file-reading-results {
  color: var(--text-primary);
}

.reading-metadata {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  flex-wrap: wrap;
  gap: 1rem;
}

.reading-metadata > div {
  font-size: 0.95rem;
}

.errors-count {
  color: var(--danger-accent);
}

.overall-summary {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: rgba(118, 143, 255, 0.1);
  border-radius: 8px;
  border: 1px solid var(--primary-accent);
}

.overall-summary h3 {
  margin: 0 0 1rem 0;
  color: var(--primary-accent);
  font-size: 1.2rem;
}

.summary-content {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  max-height: 300px;
  overflow-y: auto;
}

.files-content h3 {
  margin: 0 0 1.5rem 0;
  color: var(--text-primary);
  font-size: 1.2rem;
}

.file-content-item {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border-left: 4px solid var(--success-accent);
}

.file-content-item .file-header {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.file-content-item h4 {
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: var(--text-primary);
}

.file-content-item h4 code {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  font-family: "Fira Code", "Courier New", Courier, monospace;
  color: var(--success-accent);
  font-size: 0.9rem;
}

.error-badge {
  background-color: var(--danger-accent);
  color: white;
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.file-error {
  padding: 1rem;
  background-color: rgba(239, 83, 80, 0.1);
  border-radius: 6px;
  border: 1px solid var(--danger-accent);
  color: var(--danger-accent);
}

.file-analysis {
  margin-bottom: 1.5rem;
}

.file-analysis h5 {
  margin: 0 0 0.8rem 0;
  color: var(--primary-accent);
  font-size: 1rem;
}

.analysis-content {
  background-color: rgba(118, 143, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(118, 143, 255, 0.2);
  max-height: 250px;
  overflow-y: auto;
}

.file-content h5 {
  margin: 0 0 0.8rem 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

.content-preview {
  background-color: rgba(0, 0, 0, 0.4);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  max-height: 400px;
  overflow-y: auto;
}

.errors-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: rgba(239, 83, 80, 0.1);
  border-radius: 8px;
  border: 1px solid var(--danger-accent);
}

.errors-section h3 {
  margin: 0 0 1rem 0;
  color: var(--danger-accent);
  font-size: 1.1rem;
}

.errors-section ul {
  margin: 0;
  padding-left: 1.5rem;
  color: var(--danger-accent);
}

.errors-section li {
  margin-bottom: 0.5rem;
}

/* --- Setup Chain Results Styles --- */
.setup-chain-box {
  margin-top: 2rem;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: left;
  border: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.setup-chain-box.generating .status-header strong { color: var(--running-accent); }
.setup-chain-box.completed .status-header strong { color: var(--success-accent); }
.setup-chain-box.failed .status-header strong { color: var(--danger-accent); }

.setup-chain-results {
  color: var(--text-primary);
}

.setup-metadata {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  flex-wrap: wrap;
  gap: 1rem;
}

.setup-metadata > div {
  font-size: 0.95rem;
}

.setup-summary, .architecture-overview {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: rgba(118, 143, 255, 0.1);
  border-radius: 8px;
  border: 1px solid var(--primary-accent);
}

.setup-summary h3, .architecture-overview h3 {
  margin: 0 0 1rem 0;
  color: var(--primary-accent);
  font-size: 1.2rem;
}

.setup-steps h3 {
  margin: 0 0 1.5rem 0;
  color: var(--text-primary);
  font-size: 1.3rem;
}

.setup-step {
  margin-bottom: 2.5rem;
  padding: 2rem;
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border-left: 4px solid var(--success-accent);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.step-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.step-header h4 {
  margin: 0 0 0.8rem 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.estimated-time {
  background-color: rgba(255, 165, 38, 0.2);
  color: var(--running-accent);
  padding: 0.3rem 0.8rem;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 500;
}

.step-description {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.5;
  font-size: 0.95rem;
}

.step-content {
  display: grid;
  gap: 1.5rem;
}

.step-section h5 {
  margin: 0 0 0.8rem 0;
  color: var(--primary-accent);
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.step-section code {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  font-family: "Fira Code", "Courier New", Courier, monospace;
  color: var(--success-accent);
  font-size: 0.9rem;
}

.commands-list {
  background-color: rgba(0, 0, 0, 0.4);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  padding: 1rem;
}

.command-item {
  margin-bottom: 0.8rem;
  padding: 0.6rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border-left: 3px solid var(--primary-accent);
}

.command-item:last-child {
  margin-bottom: 0;
}

.command-item code {
  background: none;
  padding: 0;
  color: #e0e0e0;
  font-size: 0.9rem;
  word-break: break-all;
}

.step-section ul {
  margin: 0;
  padding-left: 1.5rem;
  color: var(--text-secondary);
}

.step-section li {
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.important-notes {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: rgba(255, 165, 38, 0.1);
  border-radius: 8px;
  border: 1px solid var(--running-accent);
}

.important-notes h3 {
  margin: 0 0 1rem 0;
  color: var(--running-accent);
  font-size: 1.2rem;
}

.important-notes ul {
  margin: 0;
  padding-left: 1.5rem;
  color: var(--text-primary);
}

.important-notes li {
  margin-bottom: 0.8rem;
  line-height: 1.5;
}

.troubleshooting {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: rgba(239, 83, 80, 0.1);
  border-radius: 8px;
  border: 1px solid var(--danger-accent);
}

.troubleshooting h3 {
  margin: 0 0 1.5rem 0;
  color: var(--danger-accent);
  font-size: 1.2rem;
}

.troubleshooting-item {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
  border-left: 3px solid var(--danger-accent);
}

.troubleshooting-item:last-child {
  margin-bottom: 0;
}

.troubleshooting-item h5 {
  margin: 0 0 0.8rem 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.troubleshooting-item p {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* --- Execution Results Styles --- */
.execution-box {
  margin-top: 2rem;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: left;
  border: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.execution-box.starting .status-header strong { color: var(--running-accent); }
.execution-box.running .status-header strong { color: var(--running-accent); }
.execution-box.completed .status-header strong { color: var(--success-accent); }
.execution-box.failed .status-header strong { color: var(--danger-accent); }
.execution-box.stopped .status-header strong { color: var(--text-secondary); }

.stop-btn {
  background-color: var(--danger-accent);
  color: white;
  font-size: 0.9rem;
  padding: 0.4rem 0.8rem;
}

.stop-btn:hover {
  background-color: #f67a78;
}

.execution-results {
  color: var(--text-primary);
}

.execution-metadata {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  flex-wrap: wrap;
  gap: 1rem;
}

.execution-metadata > div {
  font-size: 0.95rem;
}

.execution-progress {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: rgba(118, 143, 255, 0.1);
  border-radius: 8px;
  border: 1px solid var(--primary-accent);
}

.execution-progress h3 {
  margin: 0 0 1rem 0;
  color: var(--primary-accent);
  font-size: 1.2rem;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-accent), var(--success-accent));
  border-radius: 10px;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  color: var(--text-primary);
  font-weight: 500;
}

.execution-steps h3 {
  margin: 0 0 1.5rem 0;
  color: var(--text-primary);
  font-size: 1.3rem;
}

.execution-step {
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border-left: 4px solid var(--border-color);
}

.execution-step.success {
  border-left-color: var(--success-accent);
}

.execution-step.failed {
  border-left-color: var(--danger-accent);
}

.execution-step.running {
  border-left-color: var(--running-accent);
}

.execution-step.error_recovery {
  border-left-color: #ff9800;
}

.execution-step .step-header h4 {
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--text-primary);
  font-size: 1rem;
}

.retry-count {
  background-color: rgba(255, 165, 38, 0.2);
  color: var(--running-accent);
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.step-error {
  margin-top: 1rem;
  padding: 1rem;
  background-color: rgba(239, 83, 80, 0.1);
  border-radius: 6px;
  border: 1px solid var(--danger-accent);
  color: var(--danger-accent);
  font-size: 0.9rem;
}

.fixes-applied {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: rgba(255, 152, 0, 0.1);
  border-radius: 8px;
  border: 1px solid #ff9800;
}

.fixes-applied h3 {
  margin: 0 0 1rem 0;
  color: #ff9800;
  font-size: 1.2rem;
}

.fixes-applied ul {
  margin: 0;
  padding-left: 1.5rem;
  color: var(--text-primary);
}

.fixes-applied li {
  margin-bottom: 0.8rem;
  line-height: 1.5;
}

.execution-logs {
  margin-bottom: 2rem;
}

.execution-logs h3 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  font-size: 1.2rem;
}

.logs-container {
  background-color: rgba(0, 0, 0, 0.4);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  padding: 1rem;
  max-height: 400px;
  overflow-y: auto;
  font-family: "Fira Code", "Courier New", Courier, monospace;
}

.log-entry {
  margin-bottom: 0.3rem;
  padding: 0.2rem 0;
  color: #e0e0e0;
  font-size: 0.85rem;
  line-height: 1.4;
  word-break: break-all;
}

.execution-error {
  padding: 1.5rem;
  background-color: rgba(239, 83, 80, 0.1);
  border-radius: 8px;
  border: 1px solid var(--danger-accent);
}

.execution-error h3 {
  margin: 0 0 1rem 0;
  color: var(--danger-accent);
  font-size: 1.2rem;
}

.execution-error p {
  margin: 0;
  color: var(--danger-accent);
  line-height: 1.5;
}