# Repository Analysis Pipeline

A comprehensive microservices-based system for analyzing GitHub repositories and generating setup instructions.

## Overview

This system provides an end-to-end pipeline for analyzing GitHub repositories:

1. **GitCrawl** - Crawls repository structure using Docker containers
2. **GitAnalyser** - Identifies critical files using AI or rule-based analysis  
3. **ReadFiles** - Reads and analyzes file contents with AI insights
4. **RunRepo** - Generates comprehensive setup chains for running applications
5. **Frontend** - React-based web interface for the entire pipeline

## Features

- 🤖 **AI-Powered Analysis** - Uses Gemini AI for intelligent file analysis and setup generation
- 🐳 **Docker Integration** - Secure repository crawling in isolated containers
- 🔄 **Complete Pipeline** - From repository URL to detailed setup instructions
- 🎯 **Smart File Detection** - Automatically identifies important configuration files
- 📋 **Fallback Support** - Works without AI using template-based analysis
- 🌐 **Web Interface** - User-friendly React frontend
- 🔧 **Comprehensive Setup** - Generates step-by-step instructions with time estimates

## Quick Start

### Prerequisites

- Python 3.x
- Node.js and npm
- Docker (for GitCrawl service)
- Gemini API key (optional, for AI features)

### Easy Setup

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd <repository-name>
   ```

2. **Set up environment variables:**
   ```bash
   export GEMINI_API_KEY="your-gemini-api-key"  # Optional for AI features
   ```

3. **Start all services:**
   ```bash
   python start_all_services.py
   ```

4. **Open the frontend:**
   Navigate to http://localhost:5173

### Manual Setup

If you prefer to start services individually:

1. **Install dependencies for each service:**
   ```bash
   # For each Python service
   cd GitCrawl && pip install -r requirements.txt && cd ..
   cd GitAnalyser && pip install -r requirements.txt && cd ..
   cd ReadFiles && pip install -r requirements.txt && cd ..
   cd RunRepo && pip install -r requirements.txt && cd ..
   
   # For frontend
   cd frontend && npm install && cd ..
   ```

2. **Start services in separate terminals:**
   ```bash
   # Terminal 1: GitCrawl (port 8000)
   cd GitCrawl && python app.py
   
   # Terminal 2: GitAnalyser (port 8001)  
   cd GitAnalyser && python app.py
   
   # Terminal 3: ReadFiles (port 8003)
   cd ReadFiles && python app.py
   
   # Terminal 4: RunRepo (port 8004)
   cd RunRepo && python app.py
   
   # Terminal 5: Frontend (port 5173)
   cd frontend && npm run dev
   ```

## Services

### GitCrawl (Port 8000)
- Crawls GitHub repositories using Docker containers
- Returns complete directory structure
- Provides secure isolation for repository analysis

### GitAnalyser (Port 8001)
- Identifies critical files for analysis
- Uses AI or rule-based detection
- Prioritizes configuration and setup files

### ReadFiles (Port 8003)
- Reads file contents from GitHub repositories
- Provides AI-powered analysis of each file
- Generates comprehensive repository summaries

### RunRepo (Port 8004) - **NEW**
- Generates step-by-step setup chains
- Detects technology stacks automatically
- Provides time estimates and troubleshooting tips
- Supports both AI and template-based generation

### Frontend (Port 5173)
- React-based web interface
- Integrates all services in a seamless workflow
- Real-time progress tracking
- Rich visualization of results

## API Workflow

The complete pipeline follows this flow:

```
1. POST /crawl → GitCrawl
   ↓
2. POST /identify-files → GitAnalyser  
   ↓
3. POST /read-files → ReadFiles
   ↓
4. POST /generate-setup-chain → RunRepo (NEW)
```

## Environment Variables

- `GEMINI_API_KEY` - Required for AI features across all services
- `PORT` - Custom port for each service (optional)
- `FLASK_ENV` - Set to "development" for debug mode

## Testing

Test individual services:

```bash
# Test RunRepo service
python test_runrepo_integration.py

# Test ReadFiles service  
python test_readfiles_integration.py
```

## Example Usage

1. Enter a GitHub repository URL in the frontend
2. The system automatically:
   - Crawls the repository structure
   - Identifies important files
   - Reads and analyzes file contents
   - Generates a comprehensive setup chain
3. View detailed setup instructions with:
   - Step-by-step commands
   - Prerequisites and dependencies
   - Time estimates
   - Troubleshooting tips

## Technology Stack

- **Backend**: Python, Flask, Gemini AI
- **Frontend**: React, Vite
- **Containerization**: Docker
- **Message Queuing**: Redis (for some applications)
- **AI**: Google Generative AI (Gemini)

## Architecture

```
Frontend (React)
    ↓
GitCrawl → GitAnalyser → ReadFiles → RunRepo
    ↓           ↓           ↓          ↓
 Docker    Gemini AI   Gemini AI  Gemini AI
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test with the provided test scripts
5. Submit a pull request

## License

[Add your license information here]

## Support

For issues and questions:
1. Check the individual service README files
2. Review the test scripts for usage examples
3. Check service logs for debugging information
