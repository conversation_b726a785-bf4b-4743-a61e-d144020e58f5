import time
import threading
import logging
from typing import Dict, List, Callable, Optional
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import psutil
import docker

logger = logging.getLogger("ExecutionMonitor")

@dataclass
class ResourceUsage:
    cpu_percent: float
    memory_mb: float
    disk_io_mb: float
    network_io_mb: float
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class ExecutionMetrics:
    execution_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_seconds: float = 0
    resource_usage: List[ResourceUsage] = field(default_factory=list)
    peak_memory_mb: float = 0
    avg_cpu_percent: float = 0
    total_commands: int = 0
    successful_commands: int = 0
    failed_commands: int = 0
    retries: int = 0
    fixes_applied: int = 0

class ExecutionMonitor:
    """
    Real-time monitoring system for Docker execution agents.
    Tracks resource usage, performance metrics, and execution health.
    """
    
    def __init__(self):
        self.monitored_executions: Dict[str, ExecutionMetrics] = {}
        self.docker_client = docker.from_env()
        self._monitoring_active = True
        self._monitor_thread = None
        self._callbacks: Dict[str, List[Callable]] = {}
        
        # Start monitoring thread
        self._start_monitoring()
    
    def start_monitoring_execution(self, execution_id: str, container_id: str = None) -> None:
        """Start monitoring a specific execution."""
        logger.info(f"Starting monitoring for execution {execution_id}")
        
        metrics = ExecutionMetrics(
            execution_id=execution_id,
            start_time=datetime.now()
        )
        
        self.monitored_executions[execution_id] = metrics
        
        # Start container-specific monitoring if container ID is provided
        if container_id:
            self._start_container_monitoring(execution_id, container_id)
    
    def stop_monitoring_execution(self, execution_id: str) -> ExecutionMetrics:
        """Stop monitoring an execution and return final metrics."""
        logger.info(f"Stopping monitoring for execution {execution_id}")
        
        if execution_id in self.monitored_executions:
            metrics = self.monitored_executions[execution_id]
            metrics.end_time = datetime.now()
            metrics.duration_seconds = (metrics.end_time - metrics.start_time).total_seconds()
            
            # Calculate averages
            if metrics.resource_usage:
                metrics.avg_cpu_percent = sum(r.cpu_percent for r in metrics.resource_usage) / len(metrics.resource_usage)
                metrics.peak_memory_mb = max(r.memory_mb for r in metrics.resource_usage)
            
            return metrics
        
        return None
    
    def update_command_metrics(self, execution_id: str, success: bool, retried: bool = False, fixed: bool = False) -> None:
        """Update command execution metrics."""
        if execution_id in self.monitored_executions:
            metrics = self.monitored_executions[execution_id]
            metrics.total_commands += 1
            
            if success:
                metrics.successful_commands += 1
            else:
                metrics.failed_commands += 1
            
            if retried:
                metrics.retries += 1
            
            if fixed:
                metrics.fixes_applied += 1
    
    def get_execution_metrics(self, execution_id: str) -> Optional[ExecutionMetrics]:
        """Get current metrics for an execution."""
        return self.monitored_executions.get(execution_id)
    
    def get_all_metrics(self) -> Dict[str, ExecutionMetrics]:
        """Get metrics for all monitored executions."""
        return self.monitored_executions.copy()
    
    def add_callback(self, execution_id: str, callback: Callable[[ExecutionMetrics], None]) -> None:
        """Add a callback to be called when metrics are updated."""
        if execution_id not in self._callbacks:
            self._callbacks[execution_id] = []
        self._callbacks[execution_id].append(callback)
    
    def remove_callbacks(self, execution_id: str) -> None:
        """Remove all callbacks for an execution."""
        if execution_id in self._callbacks:
            del self._callbacks[execution_id]
    
    def _start_monitoring(self) -> None:
        """Start the main monitoring thread."""
        self._monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self._monitor_thread.start()
        logger.info("Execution monitoring started")
    
    def _monitoring_loop(self) -> None:
        """Main monitoring loop that runs in a separate thread."""
        while self._monitoring_active:
            try:
                self._collect_system_metrics()
                time.sleep(5)  # Collect metrics every 5 seconds
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(10)  # Wait longer on error
    
    def _collect_system_metrics(self) -> None:
        """Collect system-wide metrics."""
        try:
            # Get system CPU and memory usage
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk_io = psutil.disk_io_counters()
            network_io = psutil.net_io_counters()
            
            # Update metrics for all active executions
            for execution_id, metrics in self.monitored_executions.items():
                if metrics.end_time is None:  # Only for active executions
                    resource_usage = ResourceUsage(
                        cpu_percent=cpu_percent,
                        memory_mb=memory.used / (1024 * 1024),
                        disk_io_mb=(disk_io.read_bytes + disk_io.write_bytes) / (1024 * 1024) if disk_io else 0,
                        network_io_mb=(network_io.bytes_sent + network_io.bytes_recv) / (1024 * 1024) if network_io else 0
                    )
                    
                    metrics.resource_usage.append(resource_usage)
                    
                    # Keep only last 100 measurements to prevent memory bloat
                    if len(metrics.resource_usage) > 100:
                        metrics.resource_usage = metrics.resource_usage[-100:]
                    
                    # Trigger callbacks
                    self._trigger_callbacks(execution_id, metrics)
        
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
    
    def _start_container_monitoring(self, execution_id: str, container_id: str) -> None:
        """Start monitoring a specific Docker container."""
        def monitor_container():
            try:
                container = self.docker_client.containers.get(container_id)
                
                while self._monitoring_active and execution_id in self.monitored_executions:
                    try:
                        # Get container stats
                        stats = container.stats(stream=False)
                        
                        # Parse CPU usage
                        cpu_delta = stats['cpu_stats']['cpu_usage']['total_usage'] - \
                                   stats['precpu_stats']['cpu_usage']['total_usage']
                        system_delta = stats['cpu_stats']['system_cpu_usage'] - \
                                      stats['precpu_stats']['system_cpu_usage']
                        
                        cpu_percent = 0.0
                        if system_delta > 0:
                            cpu_percent = (cpu_delta / system_delta) * len(stats['cpu_stats']['cpu_usage']['percpu_usage']) * 100.0
                        
                        # Parse memory usage
                        memory_usage = stats['memory_stats']['usage'] / (1024 * 1024)  # Convert to MB
                        
                        # Update container-specific metrics
                        if execution_id in self.monitored_executions:
                            metrics = self.monitored_executions[execution_id]
                            
                            # Add container-specific resource usage
                            container_usage = ResourceUsage(
                                cpu_percent=cpu_percent,
                                memory_mb=memory_usage,
                                disk_io_mb=0,  # Docker stats don't provide easy disk I/O
                                network_io_mb=0  # Docker stats don't provide easy network I/O
                            )
                            
                            # Replace the last system metric with container-specific one
                            if metrics.resource_usage:
                                metrics.resource_usage[-1] = container_usage
                        
                        time.sleep(5)
                        
                    except docker.errors.NotFound:
                        logger.info(f"Container {container_id} no longer exists")
                        break
                    except Exception as e:
                        logger.error(f"Error monitoring container {container_id}: {e}")
                        time.sleep(10)
                        
            except Exception as e:
                logger.error(f"Error starting container monitoring for {container_id}: {e}")
        
        # Start container monitoring in a separate thread
        container_thread = threading.Thread(target=monitor_container, daemon=True)
        container_thread.start()
    
    def _trigger_callbacks(self, execution_id: str, metrics: ExecutionMetrics) -> None:
        """Trigger all callbacks for an execution."""
        if execution_id in self._callbacks:
            for callback in self._callbacks[execution_id]:
                try:
                    callback(metrics)
                except Exception as e:
                    logger.error(f"Error in callback for execution {execution_id}: {e}")
    
    def get_performance_summary(self, execution_id: str) -> Dict:
        """Get a performance summary for an execution."""
        metrics = self.get_execution_metrics(execution_id)
        if not metrics:
            return {}
        
        success_rate = (metrics.successful_commands / metrics.total_commands * 100) if metrics.total_commands > 0 else 0
        
        return {
            "execution_id": execution_id,
            "duration_seconds": metrics.duration_seconds,
            "total_commands": metrics.total_commands,
            "success_rate": round(success_rate, 1),
            "retries": metrics.retries,
            "fixes_applied": metrics.fixes_applied,
            "peak_memory_mb": round(metrics.peak_memory_mb, 1),
            "avg_cpu_percent": round(metrics.avg_cpu_percent, 1),
            "status": "completed" if metrics.end_time else "running"
        }
    
    def cleanup_old_metrics(self, max_age_hours: int = 24) -> int:
        """Clean up metrics older than specified hours."""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        cleaned_count = 0
        
        executions_to_remove = []
        for execution_id, metrics in self.monitored_executions.items():
            if metrics.end_time and metrics.end_time < cutoff_time:
                executions_to_remove.append(execution_id)
        
        for execution_id in executions_to_remove:
            del self.monitored_executions[execution_id]
            self.remove_callbacks(execution_id)
            cleaned_count += 1
        
        logger.info(f"Cleaned up {cleaned_count} old execution metrics")
        return cleaned_count
    
    def shutdown(self) -> None:
        """Shutdown the monitoring system."""
        logger.info("Shutting down execution monitor")
        self._monitoring_active = False
        
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=5)

# Global monitor instance
execution_monitor = ExecutionMonitor()
